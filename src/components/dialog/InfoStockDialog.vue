<template>
    <q-dialog v-model="dialogStore.isOpenInfo" persistent>
        <q-card style="max-width: 1100px; width: 1100px">
            <q-card-section>
                <div class="row items-center justify-between">
                    <div class="text-h6 text-weight-bold">รายละเอียดสินค้า</div>
                    <q-btn icon="close" @click="closeDialog" flat rounded />
                </div>
            </q-card-section>
            <q-separator />
            <q-card-section style="max-height: 750px" class="scroll-container">
                <!-- ชื่อ และจำนวนสินค้า -->
                <div class="row">
                    <div class="col-6">
                        <div class="gap-container">
                            <div class="text-white shadow-2 container-header-half">ชื่อ และจำนวนสินค้า
                                <q-toggle v-model="store.form.product.isactive" label="Active"
                                    class="input-container text-black q-my-sm q-pr-sm q-ml-sm mini-container"
                                    :disable="true" />
                            </div>
                            <div class="shadow-2 container-half">
                                <div class="row q-col-gutter-md">
                                    <div class="col-3 flex flex-center">รหัสสินค้า</div>
                                    <div class="col-9">
                                        <q-input class="input-container" v-model="store.form.product.id" dense readonly
                                            borderless>
                                        </q-input>
                                    </div>
                                    <div class="col-3 flex flex-center">ชื่อสามัญ</div>
                                    <div class="col-9">
                                        <q-input class="input-container" v-model="store.form.product.generic_name" dense
                                            borderless readonly />
                                    </div>
                                    <div class="col-3 flex flex-center">ชื่อสินค้า</div>
                                    <div class="col-9">
                                        <q-input class="input-container" v-model="store.form.product.product_name" dense
                                            borderless readonly />
                                    </div>
                                    <div class="col-3 flex flex-center">ทุนมาตรฐาน</div>
                                    <div class="col-9">
                                        <q-input class="input-container" v-model="store.form.product.standard_cost"
                                            dense borderless type="number" readonly />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="gap-container-left">
                            <div class="text-white shadow-2 container-header-half items-center">
                                จำนวนคงเหลือ
                            </div>
                            <div class="shadow-2 container-half flex column items-center">
                                <div style="margin-top: 30px; width: 100%;">
                                    <!-- จำนวนคงเหลือ -->
                                    <div class="row items-center q-mb-md justify-center text-row">
                                        <div class="col-label">จำนวนคงเหลือ</div>
                                        <div class="col-value">
                                            <q-input class="input-container text-center" v-model="store.form.remaining"
                                                dense borderless readonly />
                                        </div>
                                    </div>

                                    <!-- หน่วย -->
                                    <div class="row items-center q-mb-md justify-center text-row">
                                        <div class="col-label">หน่วย</div>
                                        <div class="col-value">
                                            <q-input class="input-container text-center"
                                                v-model="store.form.product.unit" dense borderless readonly />
                                        </div>
                                    </div>

                                    <!-- สถานะ -->
                                    <div class="row items-center q-mb-md justify-center text-row">
                                        <div class="col-label">สถานะ</div>
                                        <div class="col-value">
                                            <q-input v-model="store.form.status" dense borderless class="text-center"
                                                :class="statusColor" readonly />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>



                </div>

                <!-- ข้อความอธิบายสินค้า -->
                <div class="gap-container">
                    <div class="text-white shadow-2 container-header row items-center">
                        <div>ข้อความอธิบายสินค้า</div>
                    </div>
                    <div class="shadow-2 container">
                        <div class="row justify-between">
                            <div class="col-3 mini-container width-column">
                                <div class="row items-center q-my-sm">
                                    <label class="col-4 text-left q-pr-md">สถานที่เก็บ</label>
                                    <q-input dense borderless class="input-container-v2 col-8 border-0"
                                        v-model="store.form.product.storage_location" readonly />
                                </div>

                                <div class="row items-center q-mb-sm">
                                    <label class="col-4 text-left q-pr-md">Stock</label>
                                    <q-input dense borderless class="input-container-v2-stock col-3 q-mr-sm text-right"
                                        v-model="store.form.product.stock_min" label="min" readonly />
                                    <q-input dense borderless class="input-container-v2-stock col-3"
                                        v-model="store.form.product.stock_max" label="max" readonly />
                                </div>

                                <div class="row items-center q-mb-sm">
                                    <label class="col-4 text-left q-pr-md">ขนาดบรรจุ</label>
                                    <q-input dense borderless class="input-container-v2 col-8"
                                        v-model="store.form.product.packing_size" readonly>
                                    </q-input>
                                </div>

                                <div class="row items-center q-mb-sm">
                                    <label class="col-4 text-left q-pr-md">Reg No.</label>
                                    <q-input dense borderless class="input-container-v2 col-8"
                                        v-model="store.form.product.reg_no" readonly />
                                </div>
                            </div>

                            <!-- คอลัมน์กลาง: บริษัทที่ผลิต + ช่องใช้ + หมายเหตุราคาทุน -->
                            <div class="col-3 width-column">
                                <q-input v-if="store.form.product.manufacturer"
                                    v-model="store.form.product.manufacturer.name" dense borderless
                                    label="บริษัทที่ผลิต" class="input-container" />
                                <div class="q-mt-md">ข้อบ่งใช้</div>
                                <q-input v-model="store.form.product.indications" dense borderless
                                    class="input-container" type="textarea" readonly>

                                </q-input>
                            </div>

                            <!-- คอลัมน์ขวา: บริษัทจำหน่าย + คำเตือน + ข้อความแจ้งเตือน -->
                            <div class="col-3 width-column">
                                <q-input v-if="store.form.product.distributor"
                                    v-model="store.form.product.distributor.name" dense borderless
                                    label="บริษัทที่จำหน่าย" class="input-container" readonly></q-input>
                                <div class="q-mt-md text-red">คำเตือน</div>
                                <q-input v-model="store.form.product.warnings" dense borderless class="input-container"
                                    type="textarea" readonly />
                            </div>
                        </div>

                        <!-- แถวที่ 2: หมายเหตุการสั่งซื้อ -->
                        <div class="row justify-between q-mt-md">
                            <div class="col-3 width-column">
                                <div>หมายเหตุการสั่งซื้อ</div>
                                <q-input v-model="store.form.product.purchase_notes" dense borderless
                                    class="input-container" readonly />
                            </div>
                            <div class="col-3 width-column">
                                <div>หมายเหตุราคาทุน</div>
                                <q-input v-model="store.form.product.cost_notes" dense borderless
                                    class="input-container" readonly />
                            </div>
                            <div class="col-3 width-column">
                                <div>ข้อความแจ้งเตือนเมื่อขาย</div>
                                <q-input v-model="store.form.product.sales_alert_message" dense borderless
                                    class="input-container" readonly />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- กลุ่มสินค้า -->
                <div class="gap-container">
                    <div class="text-white shadow-2 container-header row items-center">
                        <div>กลุ่มสินค้า</div>
                    </div>
                    <div class="shadow-2 container">
                        <div class="row justify-between">
                            <div class="col-5 width-column-groupproduct">
                                <div>กลุ่มชื่อสามัญ</div>
                                <q-input class="input-container" v-model="store.form.product.generic_name" dense
                                    borderless readonly>
                                </q-input>
                                <div class="q-mt-md">กลุ่มรายงานพิเศษ</div>
                                <q-input class="input-container"
                                    v-if="store.form.product.special_report_group !== null && store.form.product.special_report_group !== undefined"
                                    v-model="store.form.product.special_report_group.name" dense borderless readonly>
                                </q-input>

                            </div>
                            <div class="col-5 width-column-groupproduct">
                                <div>กลุ่มสินค้า</div>
                                <q-input v-model="store.form.product.generic_group" dense borderless
                                    class="input-container" readonly />
                                <div class="q-mt-md">กลุ่มควบคุมการขาย(ขายส่ง)</div>
                                <q-input class="input-container" v-model="store.form.product.wholesale_control_group"
                                    dense borderless readonly>
                                </q-input>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- หน่วยและราคาสินค้า -->
                <div>
                    <div class="text-white shadow-2 container-header row items-center">
                        <div>หน่วยและราคาสินค้า</div>
                    </div>
                    <div class="shadow-2 container">
                        <div class="row justify-between">
                            <div class="col-1 width-column-table-1">
                                <div class="row">
                                    <div class="col-12" style="height: 56px"></div>
                                    <div class="col-4 flex flex-center q-mb-md">หน่วย</div>
                                    <div class="col-8 q-mb-md">
                                        <q-input class="input-container" v-model="store.form.product.unit" dense
                                            borderless readonly />
                                    </div>
                                    <div class="col-4 flex flex-center">บาร์โค้ด</div>
                                    <div class="col-8">
                                        <q-input class="input-container" v-model="store.form.product.barcode" dense
                                            borderless readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-1 width-column-table-2">
                                <div class="row">
                                    <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                                        อนุญาตขาย
                                    </div>
                                    <div class="col-12 q-mb-md"
                                        style="height: 40px; background-color: #2a97af; color: white">
                                        <q-checkbox v-model="store.form.product.retail" label="ขายปลีก" />
                                    </div>
                                    <div class="col-12" style="height: 40px; background-color: #865b94; color: white">
                                        <q-checkbox v-model="store.form.product.wholesale" label="ขายส่ง" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-1 width-column-table-2">
                                <div class="row">
                                    <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                                        ระดับที่ 1
                                    </div>
                                    <div class="col-12 q-mb-md">
                                        <q-input class="column-table-2" v-model="store.form.product.retail1" dense
                                            borderless type="number" readonly />
                                    </div>
                                    <div class="col-12">
                                        <q-input class="column-table-2" v-model="store.form.product.wholesale1" dense
                                            borderless type="number" readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-1 width-column-table-2">
                                <div class="row">
                                    <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                                        ระดับที่ 2
                                    </div>
                                    <div class="col-12 q-mb-md">
                                        <q-input class="column-table-2" v-model="store.form.product.retail2" dense
                                            borderless type="number" readonly />
                                    </div>
                                    <div class="col-12">
                                        <q-input class="column-table-2" v-model="store.form.product.wholesale2" dense
                                            borderless type="number" readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-1 width-column-table-2">
                                <div class="row">
                                    <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                                        ระดับที่ 3
                                    </div>
                                    <div class="col-12 q-mb-md">
                                        <q-input class="column-table-2" v-model="store.form.product.retail3" dense
                                            borderless type="number" readonly />
                                    </div>
                                    <div class="col-12">
                                        <q-input class="column-table-2" v-model="store.form.product.wholesale3" dense
                                            borderless type="number" readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-1 width-column-table-2">
                                <div class="row">
                                    <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                                        ระดับที่ 4
                                    </div>
                                    <div class="col-12 q-mb-md">
                                        <q-input class="column-table-2" v-model="store.form.product.retail4" dense
                                            borderless type="number" readonly />
                                    </div>
                                    <div class="col-12">
                                        <q-input class="column-table-2" v-model="store.form.product.wholesale4" dense
                                            borderless type="number" readonly />
                                    </div>
                                </div>
                            </div>
                            <div class="col-1 width-column-table-2">
                                <div class="row">
                                    <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                                        ระดับที่ 5
                                    </div>
                                    <div class="col-12 q-mb-md">
                                        <q-input class="column-table-2" v-model="store.form.product.retail5" dense
                                            borderless type="number" readonly />
                                    </div>
                                    <div class="col-12">
                                        <q-input class="column-table-2" v-model="store.form.product.wholesale5" dense
                                            borderless type="number" readonly />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </q-card-section>


            <q-separator />

            <!-- <q-card-actions align="right">
                <q-btn label="ปิด" color="primary" @click="$emit('update:isOpen', false)" />
            </q-card-actions> -->
        </q-card>
    </q-dialog>
</template>
<script setup lang="ts">
import { useDialogStockStore } from "src/stores/dialog-stock";
import { useStockStore } from "src/stores/stock";
import { computed, nextTick, onMounted, watch } from "vue";
const dialogStore = useDialogStockStore()
const store = useStockStore()
watch(
    () => dialogStore.modeInfo,
    async (newMode) => {
        if (newMode === 'info') {
            await nextTick(async () => {
                await store.fetchAllStock()
            });
        }
    }
);
onMounted(async () => {
    await store.fetchAllStock()
    console.log(store.getStocks)
})

const closeDialog = async () => {
    store.resetForm()
    dialogStore.modeInfo = ''
    dialogStore.closeInfo()
    await store.fetchAllStock()
}
const statusColor = computed(() => {
    switch (store.form.status) {
        case 'สินค้าหมด': return 'red-card';
        case 'สินค้าใกล้หมด': return 'yellow-card';
        case 'สินค้าคงอยู่': return 'green-card';
        default: return '';
    }
});
</script>

<style scoped>
.green-card {
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    background-color: #439E62;
    /* สีเขียว */
    color: white;
}

.yellow-card {
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    background-color: #ED9B53;
    /* สีเหลือง */
    color: black;
}

.red-card {
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    background-color: #B53638;
    /* สีแดง */
    color: white;
}

.container {
    background-color: #deecff;
    padding: 16px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

.container-header {
    background-color: #294888;
    padding-left: 16px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    height: 55px;
}

.container-half {
    background-color: #deecff;
    padding: 16px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    height: 250px;
    width: 500px;
}

.container-header-half {
    background-color: #294888;
    padding-left: 16px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    width: 500px;
    height: 55px;
    display: flex;
    align-items: center;
}

.mini-container {
    padding-left: 10px;
    padding-right: 10px;
    background-color: white;
    border-radius: 5px;
}

.input-container {
    padding-left: 10px;
    padding-right: 10px;
    background-color: white;
    border-radius: 5px;
}


.btn-accept {
    background-color: #36b54d;
    margin-right: 10px;
}

.gap-container {
    margin-bottom: 20px;
}

.gap-container-left {
    margin-left: 5px;
}

.dialog-container {
    border-radius: 10px;
}

.input-container-v2 {
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    background-color: #83a7d8;
}

.input-container-v2-stock {
    width: 89px;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    background-color: #83a7d8;
}

.width-column {
    width: 300px;
}

.width-column-groupproduct {
    width: 450px;
}

.width-column-table-1 {
    width: 200px;
}

.width-column-table-2 {
    width: 100px;
}

.column-table {
    height: 40px;
    background-color: #294888;
    color: white;
}

.column-table-2 {
    height: 40px;
    padding-left: 10px;
    padding-right: 10px;
    background-color: white;
    border: 2px solid #294888;
}

.scroll-container {
    max-height: 750px;
    overflow-y: auto;
}

.content {
    min-height: 100%;
}

.text-row {
    display: flex;
    align-items: center;
    width: 100%;
}

.col-label {
    flex: 0 0 120px;
    text-align: right;
    padding-right: 10px;
}

.col-value {
    flex: 1;
    display: flex;
    justify-content: center;
}

.q-toggle__inner--truthy {
    opacity: 1 !important;
    /* ให้สีของ toggle ชัดขึ้น */
}
</style>