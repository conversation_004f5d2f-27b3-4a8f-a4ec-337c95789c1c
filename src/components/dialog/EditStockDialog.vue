<template>
    <q-dialog v-model="dialogStore.isOpen">
        <q-card style="max-width: 800px; width: 100%">
            <q-card-section>
                <div class="row items-center justify-between">
                    <div class="text-h6 text-weight-bold">แก้ไขคลังสินค้า</div>
                    <q-btn icon="close" @click="closeDialog" flat rounded />
                </div>
            </q-card-section>

            <q-separator />

            <q-card-section style="max-height: 600px" class="scroll">
                <!-- รายละเอียดสินค้า -->
                <div class="row">
                    <div class="col-6">
                        <div class="gap-container">
                            <div class="text-white shadow-2 container-header">รายละเอียดสินค้า</div>
                            <div class="shadow-2 container">
                                <div class="row q-col-gutter-md">
                                    <div class="col-6">
                                        <q-input v-model="store.form.product.product_code" class="input-container" dense
                                            borderless label="รหัสสินค้า" />
                                    </div>
                                    <div class="col-6">
                                        <q-input v-model="store.form.product.product_name" class="input-container"
                                            label="ชื่อสินค้า" dense borderless />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="gap-container-left">
                            <div class="text-white shadow-2 container-header">จำนวน</div>
                            <div class="shadow-2 container">
                                <div class="row q-col-gutter-md">
                                    <div class="col-6">
                                        <q-input v-model.number="store.form.remaining" type="number"
                                            class="input-container" label="จำนวนคงเหลือ" dense borderless />
                                    </div>
                                    <div class="col-6">
                                        <q-input v-model="store.form.product.unit" label="หน่วยฐาน" dense borderless
                                            class="input-container" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- จำนวนสินค้า -->

            </q-card-section>

            <q-card-actions align="center">
                <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" :disabled="!isFormValid" />
                <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
            </q-card-actions>
        </q-card>
    </q-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useStockStore } from 'src/stores/stock';
import { useDialogStockStore } from "src/stores/dialog-stock";


const store = useStockStore()
const dialogStore = useDialogStockStore()
onMounted(async () => {
    console.log(store.form)
    await store.fetchAllStock()
    store.resetForm()
})

const isFormValid = computed(() => store.form.product.product_name && store.form.remaining >= 0);

const closeDialog = async () => {
    store.resetForm()
    dialogStore.close()
    await store.fetchAllStock()
}
const saveProduct = async () => {
    try {
        if (store.form.id) {
            console.log('update')
            await store.updateOne()
        }

        return true
    } catch (error) {
        console.error('Error saving product:', error)
        return false
    }
}
const saveDialog = async () => {
    const success = await saveProduct()
    if (success) {
        if (dialogStore.mode === 'add') {
            store.resetForm()
        }
        dialogStore.isOpen = false
        await store.fetchAllStock()
    }
}


</script>

<style scoped>
.container {
    background-color: #deecff;
    padding: 16px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    width: 360px;
}

.container-header {
    background-color: #294888;
    padding: 10px;
    border-radius: 10px 10px 0 0;
    width: 360px;
}

.btn-accept {
    background-color: #36b54d;
    color: black;
    margin-right: 10px;
}

.btn-cancel {
    background-color: #B53638;
    color: black;
}

.gap-container {
    margin-bottom: 20px;
}

.gap-container-left {
    margin-left: 5px;
}

.input-container {
    padding-left: 10px;
    padding-right: 10px;
    background-color: white;
    border-radius: 5px;
}
</style>
