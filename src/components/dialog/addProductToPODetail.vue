<template>
  <q-dialog v-model="dialogBtnProduct.isOpenProduct">
    <q-card style="max-width: 1100px; width: 800px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">ค้นหาสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="row justify-between">
            <div class="row">
              <SearchProductDialogComponent v-model="stockStore.searchTextDialog" placeholder="ค้นหา" />
              <!-- </div>  -->
              <!-- <span style="color: white">ข้อความ</span> -->
              <!-- <q-input
                dense
                borderless
                v-model="stockStore.searchText"
                style="margin-left: 10px"
                class="input-container-search"
                placeholder="ค้นหา"
              />
              <q-icon
                name="search"
                style="margin-left: 20px; font-size: xx-large; color: gainsboro"
              /> -->
            </div>
            <div>
              <FilterProductDialogComponent v-model="stockStore.selectedFilterDialog" :filterOptions="filterOptions">
              </FilterProductDialogComponent>
            </div>
            <div>
              <BranchProductDialogComponent v-model="stockStore.selectedBranch"></BranchProductDialogComponent>
            </div>
          </div>
        </div>
        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="shadow-2">
            <q-table flat class="body-table" :rows="stockStore.getStocksDialog" :columns="columns" row-key="id"
              hide-bottom style="height: 300px" @row-click="open3" :pagination="pagination" :rows-per-page-options="[]">
            </q-table>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" color="white" @click="addSelectedProducts" />

        <q-btn class="btn-cancel" dense flat label="ยกเลิก" color="white" @click="closeDialog" />
      </q-card-actions>
    </q-card>
    <EditPOItemsDialog></EditPOItemsDialog>
  </q-dialog>
</template>
<script setup lang="ts">
import { useDialogPODetails } from 'src/stores/dialog-po-details'

import SearchProductDialogComponent from 'src/components/searchProductDialogComponent.vue'
import EditPOItemsDialog from 'src/components/dialog/EditPOItemsDialog.vue'
import { useStockStore } from 'src/stores/stock'
import { ref, watch } from 'vue'
import FilterProductDialogComponent from 'src/components/filterProductDialogComponent.vue'
import BranchProductDialogComponent from 'src/components/BranchProductDialogComponent.vue'
import { useBranchStore } from 'src/stores/branch'
import type { QTableColumn } from 'quasar'

import { usePurchaseOrderStore } from 'src/stores/purchaseorder'
import type { Stock } from 'src/types/stock'

const dialogBtnProduct = useDialogPODetails()
const stockStore = useStockStore()
const storeBranch = useBranchStore()

const orderStore = usePurchaseOrderStore() // เก็บรายการสินค้าที่เลือก
const pagination = ref({
  rowsPerPage: 12,
})
watch(() => orderStore.editOrderItems, (newValue) => {
  console.log("🟠 Dialog ได้รับ orderItems:", JSON.stringify(newValue));
}, { immediate: true, deep: true });

const addSelectedProducts = () => {
  console.log("🔹 ก่อนอัปเดต editOrderItems:", orderStore.editOrderItems);

  // 1️⃣ คัดกรองรายการที่ต้องเพิ่มเข้าไป
  const newItems = orderStore.editOrderItems.filter(item => {
    const existsInOrder = orderStore.orderItems.some(existing => existing.product.id === item.product.id);
    const isDeleted = orderStore.deletedIds.includes(item.product.id);

    return isDeleted || !existsInOrder; // ถ้าเคยลบให้เพิ่มใหม่ หรือยังไม่มีอยู่
  });

  console.log("🔹 สินค้าที่จะเพิ่ม:", newItems);

  if (newItems.length > 0) {
    // 2️⃣ เพิ่มสินค้าใหม่เข้าไป โดยไม่ทำให้ของเก่าหาย
    newItems.forEach(item => {
      const existingItemIndex = orderStore.orderItems.findIndex(existing => existing.product.id === item.product.id);
      if (existingItemIndex !== -1) {
        // ถ้ามีอยู่แล้ว ให้อัปเดตค่าแทน
        orderStore.orderItems[existingItemIndex] = { ...orderStore.orderItems[existingItemIndex], ...item };
      } else {
        // ถ้ายังไม่มี ให้เพิ่มใหม่
        orderStore.orderItems.push({ ...item });
      }
    });

    // 3️⃣ เอา product.id ออกจาก deletedIds ถ้ามีการเพิ่มกลับมา
    orderStore.deletedIds = orderStore.deletedIds.filter(id => !newItems.some(item => item.product.id === id));
  }

  console.log("✅ Updated orderItems:", orderStore.orderItems);
  console.log("🛑 Deleted IDs after add:", orderStore.deletedIds);

  closeDialog();
};
// const addSelectedProducts = () => {
//   console.log("🔹 ก่อนอัปเดต editOrderItems:", orderStore.editOrderItems);

//   // 1️⃣ คัดกรองรายการที่ต้องเพิ่มเข้าไป
//   const newItems = orderStore.editOrderItems.filter(item => {
//     const existsInOrder = orderStore.orderItems.some(existing => existing.product.id === item.product.id);
//     const isDeleted = orderStore.deletedIds.includes(item.product.id);

//     return isDeleted || !existsInOrder; // ถ้าเคยลบให้เพิ่มใหม่ หรือยังไม่มีอยู่
//   });

//   console.log("🔹 สินค้าที่จะเพิ่ม:", newItems);

//   if (newItems.length > 0) {
//     // 2️⃣ เพิ่มสินค้าใหม่เข้าไป โดยไม่ทำให้ของเก่าหาย
//     newItems.forEach(item => {
//       const existingItemIndex = orderStore.orderItems.findIndex(existing => existing.product.id === item.product.id);
//       if (existingItemIndex !== -1) {
//         // ถ้ามีอยู่แล้ว ให้อัปเดตค่าแทน
//         orderStore.orderItems[existingItemIndex] = { ...orderStore.orderItems[existingItemIndex], ...item };
//       } else {
//         // ถ้ายังไม่มี ให้เพิ่มใหม่
//         orderStore.orderItems.push({ ...item });
//       }
//     });

//     // 3️⃣ เอา product.id ออกจาก deletedIds ถ้ามีการเพิ่มกลับมา
//     orderStore.deletedIds = orderStore.deletedIds.filter(id => !newItems.some(item => item.product.id === id));
//   }

//   console.log("✅ Updated orderItems:", orderStore.orderItems);
//   console.log("🛑 Deleted IDs after add:", orderStore.deletedIds);

//   closeDialog();
// };




watch(
  [
    () => stockStore.searchTextDialog,
    () => stockStore.selectedFilterDialog,
    () => stockStore.selectedBranchDialog,
  ],
  async () => {
    if (dialogBtnProduct.isOpenProduct) {
      await stockStore.fetchAllStock()
      await stockStore.fetchAllStockByFilterDialog();
      await storeBranch.fetchAllBranch()

    }
  }
);


const filterOptions = ref([
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'กลุ่มชื่อสามัญ', value: 'generic_group' },
  { label: 'ข้อความเตือน', value: 'warning_message' },
  { label: 'สถานที่เก็บ', value: 'storage_location' },
])

// const onLongPress = (event: MouseEvent | TouchEvent, row: Stock) => {
//   console.log('Event:', event)
//   console.log('Long press detected:', row)
// }

const columns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'รหัส',
    field: 'id',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_code',
    label: 'รหัสสินค้า',
    field: (row) => (row.product ? row.product.product_code : '-'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_name',
    label: 'ชื่อสินค้า',
    field: (row) => (row.product ? row.product.product_name : '-'),
    align: 'left' as const,
  },
  {
    name: 'unit',
    label: 'หน่วย',
    field: (row) => (row.product ? row.product.unit : '-'),
    align: 'left' as const,
  },
  {
    name: 'remaining',
    label: 'คงเหลือ',
    field: (row) => row.remaining,
    align: 'left' as const,
  },
  //field: (row) => (row.supplier ? row.supplier.name : ''),
  {
    name: 'selling_price',
    label: 'ราคาขาย',
    field: (row) => (row.product ? row.product.selling_price : '-'),
    align: 'left' as const,
  },
]

function open3(evt: Event, row: Stock) {
  console.log("🚀 Clicked Row Object:", row);
  orderStore.formOrderItems.product = row.product
  stockStore.formOrderItems = row
  console.log(orderStore.formOrderItems)
  dialogBtnProduct.open3('add')
  console.log('Open Add ')
}

const closeDialog = () => {
  dialogBtnProduct.closeProduct()
}
// const cancelDialog = () => {
//   orderStore.editOrderItems = JSON.parse(JSON.stringify(orderStore.orderItems));
//   orderStore.editOrderItems2 = []
//   dialogBtnProduct.closeProduct()
// }
</script>
<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.container-search {
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #294888;
  width: 400px;
  height: 50px;
}

.input-container-search {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  height: 35px;
  width: 250px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.radio-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 20px;
}

.q-radio {
  margin-right: 15px;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.q-table thead tr) {
  background-color: #294888;
  color: #deecff;
}

.body-table {
  background-color: #deecff;
}
</style>
