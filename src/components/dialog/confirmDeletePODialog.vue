<template>
  <q-dialog
    :model-value="modelValue ?? false"
    @update:model-value="emit('update:modelValue', $event)"
  >
    <q-card style="min-width: 350px">
      <q-card-section class="card-header">
        <div class="row justify-between">
          <div style="font-size: 18px" class="flex flex-center">
            <q-icon name="delete" size="30px" />ลบรายการใบสั่งซื้อ
          </div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>

      <q-card-section class="flex flex-center">
        <div class="text-center" style="font-size: 18px">
          ต้องการลบรายการ <b>{{item?.code}}</b> หรือไม่
        </div>
      </q-card-section>
      <q-card-actions align="center" class="q-mb-md">
        <q-btn flat label="ยกเลิก" v-close-popup class="btn" />
        <q-btn flat label="ตกลง" @click="confirmDelete" v-close-popup class="btn" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import type { PurchaseOrder } from 'src/types/purchaseOrder'

const props = defineProps<{
  modelValue: boolean | null // ปรับ type ให้ตรงกับที่ Quasar คาดหวัง
  item: PurchaseOrder | null
}>()

const emit = defineEmits(['update:modelValue', 'confirm'])

const closeDialog = () => {
  emit('update:modelValue', false) // ปิด Dialog
}

const confirmDelete = () => {
  emit('confirm', props.item) // ส่งข้อมูลสินค้าที่จะลบกลับไป
  closeDialog()
}
</script>

<style scoped>
.card-header {
  background-color: #b53638;
  color: white;
}

.btn {
  background-color: #d9d9d9;
}
</style>
