<template>
  <q-dialog v-model="dialogStore.isOpenInfo">
    <q-card style="max-width: 1200px; width: 1000px; height: 550px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">บริษัทจำหน่าย</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="max-height: 600px" class="scroll">
        <!-- รายละเอียดบริษัท -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดบริษัทจำหน่าย
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right"></div>
                  <div class="col-8 row items-center">
                    <q-radio
                      v-model="store.form.isactive"
                      :val="true"
                      label="Active"
                      :true-value="true"
                      :false-value="false"
                      disabled
                      class="input-container q-ml-lg"
                      style="margin-left: 205px"
                    />
                  </div>
                </div>

                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">รหัสบริษัท</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.supplier_number"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">ชื่อบริษัท</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.name"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 mt-md text-center">ที่อยู่บริษัท</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.address"
                      dense
                      borderless
                      type="textarea"
                      readonly
                    />
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">ประเภทบริษัท</div>
                  <div class="col-8 flex">
                    <q-radio
                      v-model="store.form.type.id"
                      :val="2"
                      label="บริษัทที่จำหน่าย"
                      disabled
                    />
                    <q-radio
                      v-model="store.form.type.id"
                      :val="1"
                      label="บริษัทที่ผลิต"
                      class="q-ml-md"
                      disabled
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">เลขที่เสียภาษี</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.tax_number"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">ชื่อผู้ติดต่อ</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.contact_name"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">โทร</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.tel"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">แฟกซ์</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.fax"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">Email</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.email"
                      dense
                      borderless
                      readonly
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </q-card-section>
    </q-card>
  </q-dialog>
</template>


<script setup lang="ts">


import { useSupplierDialogStore } from 'src/stores/dialog-supplier'
import { useSupplierStore } from 'src/stores/supplier'
import {onMounted } from 'vue'


const supplierStore = useSupplierStore()
const store = useSupplierStore()
const dialogStore = useSupplierDialogStore()
//const isReadOnly = computed(() => dialogStore.mode === 'view')

onMounted(async () => {
  await supplierStore.loadSuppliers()
  store.resetForm()
})

const closeDialog = async () => {
  store.resetForm()
  dialogStore.closeInfo()
  await store.fetchSupplier()
}
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}
</style>
src/stores/dialog-Info-supplier
