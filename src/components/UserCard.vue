<template>
  <q-card 
    flat 
    bordered 
    class="user-card cursor-pointer" 
    @click="navigateToLeaveRequest"
    :class="{ 'user-card-hover': !loading }"
  >
    <q-card-section>
      <div class="row items-center">
        <q-avatar size="48px" class="q-mr-md">
          <q-img 
            v-if="user.image" 
            :src="user.image" 
            :alt="user.name"
          />
          <q-icon v-else name="person" color="primary" />
        </q-avatar>
        
        <div class="col">
          <div class="text-h6">{{ user.name }}</div>
          <div class="text-caption text-grey-6">
            รหัส: {{ user.id }} | {{ user.role }}
          </div>
          <div v-if="user.branch" class="text-caption text-blue">
            สาขา: {{ user.branch.name }}
          </div>
        </div>
        
        <div class="col-auto">
          <q-btn 
            flat 
            round 
            icon="calendar_today" 
            color="primary"
            @click.stop="navigateToLeaveRequest"
            :loading="loading"
          >
            <q-tooltip>ดูข้อมูลการลา</q-tooltip>
          </q-btn>
        </div>
      </div>
    </q-card-section>
    
    <!-- Loading overlay -->
    <q-inner-loading :showing="loading">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface User {
  id: number
  name: string
  role: string
  image?: string
  branch?: {
    id: number
    name: string
  }
}

interface Props {
  user: User
}

const props = defineProps<Props>()

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)

// Methods
const navigateToLeaveRequest = async () => {
  loading.value = true
  
  try {
    // Navigate to leave request page with user ID parameter
    await router.push({
      name: 'userLeaveRequest',
      params: { id: props.user.id.toString() }
    })
  } catch (error) {
    console.error('Error navigating to leave request page:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.user-card {
  transition: all 0.3s ease;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.user-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #294888;
}

.user-card:active {
  transform: translateY(0);
}

.cursor-pointer {
  cursor: pointer;
}
</style>
