<template>
  <q-toolbar class="bg-color q-pt-md">
    <div class="row full-width no-wrap items-center">
      <!-- ใช้ q-space เพื่อดันปุ่มที่เหลือไปทางขวา -->
      <q-space />

      <!-- ปุ่มเมนู -->
      <div class="col-auto row no-wrap">
        <q-btn
          to="/user/summary"
          unelevated
          label="ภาพรวมพนักงาน"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/user/summary' }"
        />
        <q-btn
          to="/user/management"
          unelevated
          label="จัดการพนักงาน"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/user/management' }"
        />
        <q-btn
          to="/user/checkinout"
          unelevated
          label="เข้า-ออกงาน"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/user/checkinout' }"
        />
      </div>
    </div>
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
// import { useUserStore } from 'src/stores/userStore'

const route = useRoute()
const isActive = ref(route.path)

watchEffect(() => {
  isActive.value = route.path
})
</script>

<style scoped>
.bg-color {
  background-color: #f3f3f3;
}

.active-btn {
  background-color: #609fa3 !important;
  color: white;
}

/* สีของปุ่มที่ไม่ได้กด */
.q-btn {
  background-color: #b0bec5;
  color: white;
  border-radius: 10px;
}
</style>
