<template>
  <div class="filter-box">
    <q-select v-model="selectedFilter" :options="store.getBranchs" label="สาขา" borderless dense options-dense
      behavior="menu" emit-value map-options>
      <template v-slot:selected>
        <span>{{ selectedFilterLabel }}</span>
      </template>
      <template v-slot:option="scope">
        <q-item clickable v-ripple @click="toggleFilter(scope.opt.value)">
          <q-item-section avatar>
            <q-radio v-model="selectedFilter" :val="String(scope.opt.value)" />
          </q-item-section>
          <q-item-section>
            {{ scope.opt.label }}
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>

<script setup lang="ts">
import { useBranchStore } from 'src/stores/branch'
import { useStockStore } from 'src/stores/stock'
import { ref, computed, onMounted, watch } from 'vue'

const store = useBranchStore()
const storeStock = useStockStore()
const selectedFilter = ref<string | null>(null);
watch(selectedFilter, (newValue) => {
  storeStock.selectedBranch = newValue ?? "";
});

onMounted(async () => {
  await store.fetchAllBranch()
  console.log(selectedFilter)
})



const selectedFilterLabel = computed(() => {
  return store.getBranchs.find((opt) => opt.value === selectedFilter.value)?.label
})


const toggleFilter = (value: string | number) => {
  const valueStr = String(value); // แปลงเป็น string
  selectedFilter.value = selectedFilter.value === valueStr ? null : valueStr;
};
</script>

<style scoped>
.filter-box {
  background-color: #c3e7dd;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 100%;
  max-width: 300px;
}
</style>
