<template>
  <div class="filter-box">
    <q-select
      v-model="selectedFilter"
      :options="store.getBranchs"
      label="สาขา"
      borderless
      dense
      options-dense
      behavior="menu"
      emit-value
      map-options
      class="custom-select"
    >
      <template v-slot:selected>
        <span class="selected-text">{{ selectedFilterLabel }}</span>
      </template>
      <template v-slot:option="scope">
        <q-item clickable v-ripple @click="toggleFilter(scope.opt.value)">
          <q-item-section avatar>
            <q-radio v-model="selectedFilter" :val="String(scope.opt.value)" color="black" />
          </q-item-section>
          <q-item-section>
            {{ scope.opt.label }}
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>

<script setup lang="ts">
import { useBranchStore } from 'src/stores/branch'
import { useStockStore } from 'src/stores/stock'
import { ref, computed, onMounted, watch } from 'vue'

const store = useBranchStore()
const storeStock = useStockStore()
const selectedFilter = ref<string | null>(null)
watch(selectedFilter, (newValue) => {
  storeStock.selectedBranchDialog = newValue ?? ''
})

onMounted(async () => {
  await store.fetchAllBranch()
  console.log(selectedFilter)
})

const selectedFilterLabel = computed(() => {
  return store.getBranchs.find((opt) => opt.value === selectedFilter.value)?.label
})

const toggleFilter = (value: string | number) => {
  const valueStr = String(value) // แปลงเป็น string
  selectedFilter.value = selectedFilter.value === valueStr ? null : valueStr
}
</script>

<style scoped>
.filter-box {
  background-color: #294888;
  color: white;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 150px;
  height: 50px;
  margin-left: 20px;
  margin-top: 10px;
}

.custom-dropdown .q-item {
  background-color: #e9f2ff !important;
  color: black !important;
}

.custom-select :deep(.q-icon) {
  color: white;
}

.custom-select :deep(.q-field__label) {
  color: white;
}

.custom-select :deep(.q-field__control) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* เปลี่ยนสีตัวอักษรเมื่อเลือกแล้ว */
.selected-text {
  color: white;
  text-align: center;

  margin-left: 5px;
}
</style>
