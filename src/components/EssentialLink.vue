<template>
  <q-list class="text-bold q-ml-xl q-mr-xl">
    <q-item
      v-for="item in links"
      :key="item.title"
      clickable
      v-ripple
      :to="item.path"
      :class="['rounded-item', 'q-mb-md', { 'active-link': isItemActive(item) }]"
      @click="$emit('updateTitle', item.title)"
    >
      <q-item-section avatar class="flex justify-center items-center">
        <q-icon :name="item.icon" />
      </q-item-section>

      <q-item-section>
        <q-item-label>{{ item.title }}</q-item-label>
        <q-item-label v-if="item.caption" caption>{{ item.caption }}</q-item-label>
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

export interface SubItemProps {
  title: string
  caption?: string
  path: string
  icon?: string
}

export interface EssentialLinkProps {
  title: string
  caption?: string
  path?: string
  icon?: string
  subItems?: SubItemProps[]
}

defineEmits(['updateTitle'])

const route = useRoute()

const links: EssentialLinkProps[] = [
  { title: 'แดชบอร์ด', path: '/dashboard', icon: 'bar_chart' },
  { title: 'หน้าขาย', path: '/sales', icon: 'shopping_cart' },
  { title: 'สต็อกสินค้า', path: '/stock/inventory', icon: 'inventory_2' },
  { title: 'บริษัทจำหน่าย', path: '/suppliers', icon: 'apartment' },
  { title: 'พนักงาน', path: '/user/summary', icon: 'person' },
  {
    title: 'ส่งสินค้าสาขา', path: '/branch/sto', icon: 'local_shipping',
    subItems: [
      { title: 'ส่งสินค้าสาขา', path: '/branch/sts', icon: 'local_shipping' },
      { title: 'ส่งสินค้าสาขา', path: '/branch/br', icon: 'local_shipping' },
    ]
  },
]

/**
 * Check if a navigation item should be considered active
 * For the user/employee section, it should be active for any /user/* route
 * For other sections, use exact path matching
 */
const isItemActive = computed(() => {
  return (item: EssentialLinkProps) => {
    if (!item.path) return false

    // Special handling for user/employee navigation
    // Should be active for any route that starts with /user/
    if (item.path.startsWith('/user/')) {
      return route.path.startsWith('/user/')
    }

    // For stock navigation, check if current route starts with /stock/
    if (item.path.startsWith('/stock/')) {
      return route.path.startsWith('/stock/')
    }

    // For dashboard navigation, check if current route starts with /dashboard
    if (item.path.startsWith('/dashboard')) {
      return route.path.startsWith('/dashboard')
    }

    // For other routes, use exact matching
    return route.path === item.path
  }
})
</script>

<style scoped>
.active-link {
  background: #5da5a2;
  color: white;
  border-radius: 25px;
}

.rounded-item {
  border-radius: 25px;
}

.rounded-item:hover {
  background: rgba(93, 165, 162, 0.2);
}

.rounded-item.active-link:hover {
  background: #5da5a2;
}
</style>
