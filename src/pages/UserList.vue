<template>
  <div class="q-pa-md">
    <div class="row q-mb-md">
      <div class="col">
        <h4 class="q-my-none">รายชื่อพนักงาน</h4>
        <p class="text-grey-6">คลิกที่การ์ดพนักงานเพื่อดูข้อมูลการลา</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="row justify-center q-py-xl">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md text-grey-6">กำลังโหลดข้อมูลพนักงาน...</div>
    </div>

    <!-- User Cards Grid -->
    <div v-else class="row q-col-gutter-md">
      <div 
        v-for="user in users" 
        :key="user.id" 
        class="col-12 col-sm-6 col-md-4 col-lg-3"
      >
        <UserCard :user="user" />
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && users.length === 0" class="text-center q-py-xl">
      <q-icon name="people_outline" size="4em" color="grey-4" />
      <div class="text-h6 text-grey-6 q-mt-md">ไม่พบข้อมูลพนักงาน</div>
      <div class="text-grey-5">กรุณาลองใหม่อีกครั้ง</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import UserCard from 'src/components/UserCard.vue'
import { useUserStore } from 'src/stores/userStore'

// Types
interface User {
  id: number
  name: string
  role: string
  image?: string
  branch?: {
    id: number
    name: string
  }
}

// Store
const userStore = useUserStore()

// Reactive data
const users = ref<User[]>([])
const loading = ref(false)

// Methods
const fetchUsers = async () => {
  loading.value = true
  
  try {
    // Fetch users from the store
    await userStore.fetchUsers()
    
    // Map users to the format expected by UserCard
    users.value = userStore.users.map(user => ({
      id: user.id,
      name: user.name,
      role: user.role,
      image: user.image,
      branch: user.branch
    }))
  } catch (error) {
    console.error('Error fetching users:', error)
    users.value = []
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await fetchUsers()
})
</script>

<style scoped>
/* Add any additional styles here */
</style>
