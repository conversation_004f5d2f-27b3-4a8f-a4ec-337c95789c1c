<template>
  <StockNavigation></StockNavigation>
  <div class="container q-pa-md q-ma-md">
    <div class="row wrap-container">
      <div class="col-8 col-md-12 col-sm-12 col-xs-12 search-container">
        <div class="row items-center q-col-gutter-md">
          <div class="col">
            <searchComponent v-model="searchQuery" placeholder="ค้นหา" />
          </div>
          <div class="col-auto">
            <DateRangePicker v-model:modelValueFrom="dateFrom" v-model:modelValueTo="dateTo" />
          </div>
        </div>
      </div>

      <!-- ส่วนของตัวกรองและปุ่ม -->
      <div class="col-4 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
       <AddGRDialog></AddGRDialog>
        <q-btn flat @click="openDialog" class="add-button q-mb-md" label="รับสินค้ารายการใหม่" />
      </div>
    </div>

    <div>
      <span class="text-subtitle1 text-weight-bold q-mb-sm">เตรียมรายการ</span>
      <q-card flat class="custom-table">
        <q-table class="body-table" :rows="filteredOrders" :columns="columns" row-key="id" :pagination="pagination"
          :rows-per-page-options="[]" style="height: 100%; max-height: 700px">
        </q-table>
      </q-card>
    </div>

    <div style="margin-top: 20px;">
      <span class="text-subtitle1 text-weight-bold q-mb-sm ">กำลังดำเนินการ</span>
      <q-card flat class="custom-table">
        <q-table class="body-table" :rows="filteredOrders" :columns="columns" row-key="id" :pagination="pagination"
          :rows-per-page-options="[]" style="height: 100%; max-height: 700px">
        </q-table>
      </q-card>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import StockNavigation from 'src/components/StockNavigation.vue'
import searchComponent from 'src/components/searchComponent.vue'
import DateRangePicker from 'src/components/DateRangePicker.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import type { QTableColumn } from 'quasar'
import { usePurchaseOrderStore } from 'src/stores/purchaseorder'
import { useDialogGR } from 'src/stores/dialog-gr'
import AddGRDialog from 'src/components/dialog/goodsreceipt/AddGRDialog.vue'


const dialogGR = useDialogGR()

const pagination = ref({
  rowsPerPage: 12,
})

onMounted(async () => { })

const columns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'เลขที่รับสินค้า',
    field: 'id',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_code',
    label: 'วันที่',
    field: 'product_code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_name',
    label: 'เลขที่สั่งซื้อสินค้า',
    field: 'product_name',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'unit',
    label: 'เลขที่ภาษี',
    field: 'unit',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'wholesale_prices.wholesale1',
    label: 'วันที่ภาษี',
    field: 'wholesale1',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'wholesale_prices.wholesale1',
    label: 'บริษัทจำหน่าย',
    field: 'wholesale1',
    align: 'center' as const,
    sortable: true,
  },
  {
    name: 'wholesale_prices.wholesale1',
    label: 'รวมเงิน',
    field: 'wholesale1',
    align: 'center' as const,
    sortable: true,
  },
]

const store = usePurchaseOrderStore()
const filteredOrders = ref(store.orders)
const dateFrom = ref('')
const dateTo = ref('')
const searchQuery = ref('')
const selectedFilter = ref<string>('')

const filterOptions = [
  { label: 'บาร์โค้ด', value: 'barcode' },
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'สถานที่เก็บ', value: 'storage_location' },
  { label: 'กลุ่มชื่อสามัญ', value: 'common_name_group' },
  { label: 'ข้อความเตือน', value: 'warning_message' },
]

const openDialog = () => {
  dialogGR.open('')
  console.log('open add')
}
</script>

<style scoped>
:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  overflow: hidden;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}
</style>
