<!--
  MapCard Component
  Displays Leaflet map with location controls
-->
<template>
  <q-card flat class="map-card">
    <q-card-section class="q-pa-none">
      <div class="map-container">
        <div :id="props.mapElementId" class="leaflet-map"></div>

        <!-- Location Info Overlay -->
        <div class="location-info-card">
          <div class="location-name">{{ props.locationName }}</div>
          <div class="location-service">{{ props.locationService }}</div>
        </div>

        <!-- Map Controls -->
        <div class="map-controls">
          <q-btn
            flat
            round
            class="map-control-btn"
            icon="my_location"
            color="primary"
            :loading="isReloading"
            @click="handleLocationReload"
            :title="$t?.('getCurrentLocation') || 'Get Current Location'"
          />
          <q-btn
            flat
            round
            class="map-control-btn"
            icon="refresh"
            color="primary"
            :loading="isReloading"
            @click="handleLocationReload"
            :title="$t?.('refreshLocation') || 'Refresh Location'"
          />
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useLeafletMap } from '../composables/useLeafletMap'
import { DEFAULT_VALUES } from '../constants'

interface Props {
  mapElementId?: string
  locationName?: string
  locationService?: string
}

interface Emits {
  (e: 'locationReloaded', location: { lat: number; lng: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  mapElementId: 'leaflet-map',
  locationName: DEFAULT_VALUES.LOCATION_NAME,
  locationService: DEFAULT_VALUES.LOCATION_SERVICE,
})

const emit = defineEmits<Emits>()

// Composable for map functionality
const { mapLocation, initializeMap, reloadLocation, destroyMap } = useLeafletMap()

// Local state
const isReloading = ref(false)

/**
 * Handle location reload with loading state
 */
const handleLocationReload = () => {
  isReloading.value = true

  try {
    reloadLocation()
    emit('locationReloaded', { ...mapLocation.value })
  } catch (error) {
    console.error('Failed to reload location:', error)
  } finally {
    // Add small delay for better UX
    setTimeout(() => {
      isReloading.value = false
    }, 500)
  }
}

// Lifecycle hooks
onMounted(async () => {
  await initializeMap(props.mapElementId)
})

onUnmounted(() => {
  destroyMap()
})
</script>

<style scoped>
.map-card {
  background: white;
  border-radius: 15px;
  min-height: 300px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.map-container {
  position: relative;
  height: 300px;
  border-radius: 15px;
  overflow: hidden;
}

.leaflet-map {
  width: 100%;
  height: 300px;
  border-radius: 15px;
  z-index: 1;
}

.location-info-card {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  max-width: 80%;
  z-index: 10;
}

.location-name {
  font-weight: bold;
  font-size: 1rem;
  color: #333;
  margin-bottom: 4px;
}

.location-service {
  font-size: 0.9rem;
  color: #666;
}

.map-controls {
  position: absolute;
  right: 10px;
  bottom: 80px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.map-control-btn {
  background: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.map-control-btn:hover {
  transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .map-container {
    height: 250px;
  }

  .leaflet-map {
    height: 250px;
  }

  .location-info-card {
    max-width: 90%;
    padding: 10px 12px;
  }

  .location-name {
    font-size: 0.9rem;
  }

  .location-service {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .map-container {
    height: 200px;
  }

  .leaflet-map {
    height: 200px;
  }

  .map-controls {
    right: 8px;
    bottom: 60px;
    gap: 6px;
  }
}
</style>
