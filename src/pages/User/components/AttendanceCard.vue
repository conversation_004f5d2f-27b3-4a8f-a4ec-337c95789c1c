<template>
  <div class="col-5 q-pa-md atten-card">
    <div class="row">
      <div class="col-6">
        <p class="text-center" style="opacity: 55%">ผลรวม</p>
        <div class="text-bold text-center text-h4">{{ total }}</div>
      </div>
      <div class="col-6">
        <p class="flex justify-between" style="color: #439e62">
          <span>ตรงเวลา</span>
          <span>{{ present }}</span>
        </p>
        <p class="flex justify-between" style="color: #ed9b53">
          <span>เกินเวลา</span>
          <span>{{ late }}</span>
        </p>
        <p class="flex justify-between" style="color: #b53638">
          <span>ลา</span>
          <span>{{ sickLeave + personalLeave }}</span>
        </p>
      </div>
    </div>

    <q-separator class="q-my-sm" />

    <div class="type-emp">
      <p><span class="text-bold">ประเภท</span>&nbsp;{{ employeeType }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  total: {
    type: Number,
    default: 0,
  },
  present: {
    type: Number,
    default: 0,
  },
  late: {
    type: Number,
    default: 0,
  },
  sickLeave: {
    type: Number,
    default: 0,
  },
  personalLeave: {
    type: Number,
    default: 0,
  },
  employeeType: {
    type: String,
    required: true,
  },
})
</script>

<style scoped>
.atten-card {
  background-color: white;
  border-radius: 10px;
  min-width: 200px;
  width: 100%;
  max-width: 350px;
}

@media (max-width: 1480px) {
  .atten-card {
    max-width: 230px;
  }
}

@media (max-width: 1400px) {
  .atten-card {
    max-width: 200px;
  }
}

.type-emp {
  color: #294888;
}

p {
  margin: 0;
}
</style>
