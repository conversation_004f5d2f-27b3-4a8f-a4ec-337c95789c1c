/**
 * Composable for Leaflet map management
 */

import { ref, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { generateLocationOffset } from '../utils'
import { MAP_CONFIG } from '../constants'
import type { MapLocation } from '../types'

export const useLeafletMap = () => {
  // Reactive state
  const mapLocation = ref<MapLocation>({ ...MAP_CONFIG.DEFAULT_LOCATION })
  let leafletMap: unknown = null
  let leafletMarker: unknown = null

  /**
   * Initialize Leaflet map
   */
  const initializeMap = async (mapElementId: string) => {
    await nextTick()

    const mapDiv = document.getElementById(mapElementId)
    if (!mapDiv || leafletMap) return

    try {
      // Create map instance
      leafletMap = L.map(mapDiv).setView(
        [mapLocation.value.lat, mapLocation.value.lng],
        MAP_CONFIG.DEFAULT_ZOOM,
      )

      // Add tile layer
      L.tileLayer(MAP_CONFIG.TILE_LAYER_URL, {
        attribution: MAP_CONFIG.ATTRIBUTION,
      }).addTo(leafletMap as L.Map)

      // Add marker
      leafletMarker = L.marker([mapLocation.value.lat, mapLocation.value.lng]).addTo(
        leafletMap as L.Map,
      )

      console.log('✅ Leaflet map initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Leaflet map:', error)
    }
  }

  /**
   * Update map view and marker position
   */
  const updateMapView = (newLocation: MapLocation) => {
    if (!leafletMap || !leafletMarker) return

    try {
      const latlng = [newLocation.lat, newLocation.lng] as [number, number]
      ;(leafletMap as L.Map).setView(latlng, MAP_CONFIG.DEFAULT_ZOOM)
      ;(leafletMarker as L.Marker).setLatLng(latlng)

      mapLocation.value = { ...newLocation }
      console.log('📍 Map view updated to:', newLocation)
    } catch (error) {
      console.error('❌ Failed to update map view:', error)
    }
  }

  /**
   * Reload location with random offset (simulation)
   */
  const reloadLocation = () => {
    const newLocation = generateLocationOffset(
      MAP_CONFIG.DEFAULT_LOCATION,
      MAP_CONFIG.LOCATION_OFFSET_RANGE,
    )

    updateMapView(newLocation)
    console.log('🔄 Location reloaded with offset')
  }

  /**
   * Get current location (placeholder for real geolocation)
   */
  const getCurrentLocation = (): MapLocation => {
    // In a real application, this would use the browser's geolocation API
    // For now, return the default location with a small offset
    return generateLocationOffset(MAP_CONFIG.DEFAULT_LOCATION, MAP_CONFIG.LOCATION_OFFSET_RANGE)
  }

  /**
   * Set map location
   */
  const setLocation = (location: MapLocation) => {
    updateMapView(location)
  }

  /**
   * Destroy map instance
   */
  const destroyMap = () => {
    if (leafletMap) {
      try {
        ;(leafletMap as L.Map).remove()
        leafletMap = null
        leafletMarker = null
        console.log('🧹 Leaflet map destroyed')
      } catch (error) {
        console.error('❌ Error destroying map:', error)
      }
    }
  }

  /**
   * Check if map is initialized
   */
  const isMapInitialized = (): boolean => {
    return leafletMap !== null
  }

  return {
    // State
    mapLocation,

    // Methods
    initializeMap,
    updateMapView,
    reloadLocation,
    getCurrentLocation,
    setLocation,
    destroyMap,
    isMapInitialized,
  }
}
