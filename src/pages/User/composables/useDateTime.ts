/**
 * Composable for date and time management
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { formatCurrentDateTime, generateWeekDays } from '../utils'
import { REFRESH_INTERVALS } from '../constants'
import type { WeekDay } from '../types'

export const useDateTime = () => {
  // Reactive state
  const currentTime = ref('')
  const currentDate = ref('')
  const weekDays = ref<WeekDay[]>([])
  const timeInterval = ref<NodeJS.Timeout | null>(null)

  /**
   * Update current time and date
   */
  const updateTime = () => {
    const { currentTime: time, currentDate: date } = formatCurrentDateTime()
    currentTime.value = time
    currentDate.value = date
  }

  /**
   * Initialize week days
   */
  const initializeWeekDays = () => {
    weekDays.value = generateWeekDays()
  }

  /**
   * Start time interval
   */
  const startTimeInterval = () => {
    updateTime()
    timeInterval.value = setInterval(updateTime, REFRESH_INTERVALS.TIME_UPDATE)
  }

  /**
   * Stop time interval
   */
  const stopTimeInterval = () => {
    if (timeInterval.value) {
      clearInterval(timeInterval.value)
      timeInterval.value = null
    }
  }

  /**
   * Initialize date and time functionality
   */
  const initialize = () => {
    updateTime()
    initializeWeekDays()
    startTimeInterval()
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    stopTimeInterval()
  }

  // Lifecycle management
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    currentTime,
    currentDate,
    weekDays,
    
    // Methods
    updateTime,
    initializeWeekDays,
    startTimeInterval,
    stopTimeInterval,
    initialize,
    cleanup,
  }
}
