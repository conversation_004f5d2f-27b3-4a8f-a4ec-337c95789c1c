/**
 * TypeScript interfaces and types for User pages
 */

// Week day interface
export interface WeekDay {
  letter: string
  number: string
  isToday: boolean
}

// Map location interface
export interface MapLocation {
  lat: number
  lng: number
}

// Loading states interface
export interface LoadingStates {
  workStats: boolean
  employeeData: boolean
  workHours: boolean
  lateCheckIn: boolean
}

// Work statistics interface
export interface WorkStatistics {
  totalEmployees: number
  averageHours: number
  totalLateDays: number
}

// Employee data interface
export interface EmployeeData {
  fullTime: number
  partTime: number
}

// Chart data interfaces
export interface WorkHoursChartData {
  employeeId: number
  hours: number
  employeeName: string
}

export interface LateCheckInChartData {
  employeeId: number
  lateDays: number
  employeeName: string
}

// API response interfaces
export interface EmployeeApiData {
  userId: number
  name: string
  totalWorkHours: number
  lateDays: number
}

export interface MonthlyHoursApiResponse {
  summary: {
    totalEmployees: number
    averageWorkHours: number
    employeeTypes: {
      fullTime: number
      partTime: number
    }
    totalLateDays: number
  }
  employees: EmployeeApiData[]
}

// Filter option interface
export interface FilterOption {
  label: string
  value: string
}

// User interface (extending base user type)
export interface UserDisplayData {
  id: number
  name: string
  tel?: string
  role?: string
}

// Chart tooltip context interfaces
export interface ChartTooltipContext {
  dataIndex: number
  parsed: {
    y: number
  }
}

export interface ChartTitleContext {
  dataIndex?: number
}

// Attendance time display interface
export interface AttendanceTimeDisplay {
  checkInTime: string
  checkOutTime: string
  todayWorkHours: string | number
}

// Map control interface
export interface MapControls {
  reloadLocation: () => void
}

// Date navigation interface
export interface DateNavigation {
  currentDate: Date
  startDate: Date
  endDate: Date
  formattedStartDate: string
  formattedEndDate: string
  apiStartDate: string
  apiEndDate: string
  previousMonth: () => Promise<void>
  nextMonth: () => Promise<void>
}
