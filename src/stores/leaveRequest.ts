import { defineStore } from 'pinia';
import { LeaveRequestService } from 'src/services/leaveRequestService';
import type { LeaveRequest } from 'src/types/leaveRequest';

export const useLeaveRequestStore = defineStore('leaveRequest', {
  state: () => ({
    userLeaveRequests: [] as LeaveRequest[],
    allLeaveRequests: [] as LeaveRequest[],
    loading: false,
    error: null as string | null,
  }),

  actions: {
    // Create a new leave request
    async createLeaveRequest(userId: number, leaveDate: string, leaveType: string, reason?: string) {
      this.loading = true;
      this.error = null;
      
      try {
        const newLeaveRequest = await LeaveRequestService.create(
          userId, 
          leaveDate, 
          leaveType, 
          reason
        );
        
        // Add to user's leave requests
        this.userLeaveRequests.push(newLeaveRequest);
        return newLeaveRequest;
      } catch (err: unknown) {
        this.error = err instanceof Error 
          ? err.message 
          : (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to create leave request';
        throw err;
      } finally {
        this.loading = false;
      }
    },

    // Fetch leave requests for a specific user
    async fetchUserLeaveRequests(userId: number) {
      this.loading = true;
      this.error = null;
      
      try {
        this.userLeaveRequests = await LeaveRequestService.getUserLeaveRequests(userId);
        return this.userLeaveRequests;
      } catch (err: unknown) {
        this.error = err instanceof Error 
          ? err.message 
          : (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to fetch leave requests';
        throw err;
      } finally {
        this.loading = false;
      }
    },

    // Fetch all leave requests (admin only)
    async fetchAllLeaveRequests() {
      this.loading = true;
      this.error = null;
      
      try {
        this.allLeaveRequests = await LeaveRequestService.getAllLeaveRequests();
        return this.allLeaveRequests;
      } catch (err: unknown) {
        this.error = err instanceof Error 
          ? err.message 
          : (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to fetch all leave requests';
        throw err;
      } finally {
        this.loading = false;
      }
    },

    // Update user's day off
    async updateDayOff(userId: number, dayOff: string) {
      this.loading = true;
      this.error = null;
      
      try {
        return await LeaveRequestService.updateDayOff(userId, dayOff);
      } catch (err: unknown) {
        this.error = err instanceof Error 
          ? err.message 
          : (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to update day off';
        throw err;
      } finally {
        this.loading = false;
      }
    }
  }
});
