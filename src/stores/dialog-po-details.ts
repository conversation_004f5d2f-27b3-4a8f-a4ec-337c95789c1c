import { defineStore } from 'pinia'

export const useDialogPODetails = defineStore('dialog2', {
  state: () => ({
    isOpen2: false,
    mode: '',
    modeProduct: '',
    mode3: '',
    modeInfo: '',
    isOpenProduct: false,
    isOpen3: false,
    isOpenInfo: false,
  }),
  actions: {
    open2(mode = 'add') {
      this.isOpen2 = true
      this.mode = mode
    },
    close2() {
      this.isOpen2 = false
    },
    openProduct(modeProduct = 'addProduct') {
      this.isOpenProduct = true
      this.modeProduct = modeProduct
    },
    closeProduct() {
      this.isOpenProduct = false
    },
    open3(mode3 = 'add') {
      this.isOpen3 = true
      this.mode3 = mode3
    },
    close3() {
      this.isOpen3 = false
    },
    openInfo(modeInfo = 'info') {
      this.isOpenInfo = true
      this.modeInfo = modeInfo
    },
    closeInfo() {
      this.isOpenInfo = false
    },
  },
})
