import { defineStore } from 'pinia'

export const useDialogStockStore = defineStore('dialog', {
  state: () => ({
    isOpen: false,
    isOpenInfo: false,
    mode: '',
    modeInfo: ''
  }),
  actions: {
    open(mode = 'edit') {
      this.isOpen = true
      this.mode = mode
    },
    openInfo(modeInfo = 'info') {
      this.isOpenInfo = true
      this.modeInfo = modeInfo
    },
    close() {
      this.isOpen = false
    },
    closeInfo() {
      this.isOpenInfo = false
    },
  },
})
