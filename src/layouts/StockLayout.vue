<template>
  <q-layout view="lHh Lpr lFf">
    <q-header elevated :class="'bg-secondary'">
      <q-toolbar>
        <q-btn flat dense round icon="menu" aria-label="Menu" @click="toggleLeftDrawer" />

        <q-toolbar-title> ระบบคลังสินค้า </q-toolbar-title>

        <div>Quasar v{{ $q.version }}</div>
      </q-toolbar>
    </q-header>
    <!-- แถบปุ่มเมนูใต้ Header -->
    <q-toolbar class="bg-grey-3 q-mt-xl q-py-md q-px-sm flex justify-center">
      <q-btn to="/stock/product" color="primary" unelevated label="📦 รายการสินค้า" class="q-mx-sm" />
      <q-btn to="/stock/order" color="green" unelevated label="🛒 สั่งซื้อสินค้า" class="q-mx-sm" />
      <q-btn to="/stock/receive" color="orange" unelevated label="📥 รับสินค้า" class="q-mx-sm" />
    </q-toolbar>
    <q-drawer v-model="drawerOpen" show-if-above bordered>
      <q-list>
        <q-item-label header> </q-item-label>
        <q-item-label header class="text-black ">
          <q-img src="icon.png/header.png" width="67px" height="67px" class="q-mr-sm no-border-radius"
            style="position: absolute; left: 23px; top: 15px;"></q-img>
          <q-item-label class="text-blod text-h6"
            style="position: absolute; left: 94px; top: 30px; text-shadow:2px 2px 4px rgba(0, 0, 0, 0.3) ;">สุขถาวรโอสถ</q-item-label>
          <q-item-label class
            style="position: absolute; left: 108px; top: 45px; font-size: 11px; text-shadow:2px 2px 4px rgba(0, 0, 0, 0.3) ;">
            SUKTHAVORN OSOT</q-item-label>
        </q-item-label>
        <q-list style="position: absolute;top: 11%; width:300px ; text-align: center;">
          <EssentialLink v-for="link in linksList" :key="link.title" v-bind="link" />
        </q-list>      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view /> <!-- โหลดหน้า เช่น ProductPage, OrderPage -->
      <!-- <q-page class="q-pa-md">
        <q-item to="/stock/product" clickable v-ripple>
          <q-btn color="primary" unelevated label="📦 รายการสินค้า" />
        </q-item>
        <q-item to="/stock/order" clickable v-ripple>
          <q-btn color="green" unelevated label="🛒 สั่งซื้อสินค้า" />
        </q-item>
        <q-item to="/stock/receive" clickable v-ripple>
          <q-btn color="orange" unelevated label="📥 รับสินค้า" />
        </q-item>
      </q-page> -->
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import EssentialLink from 'src/components/EssentialLink.vue';
import type { EssentialLinkProps } from 'src/components/EssentialLink.vue';
import { ref } from 'vue'
const drawerOpen = ref(false)
const linksList: EssentialLinkProps[] = [
  {
    title: 'แดชบอร์ด',
    icon: 'dashboard',
    path: '/dashboard',
    subItems: [
      { title: 'Sales Report', path: '/dashboard/1', icon: 'bar_chart' },
      { title: 'Sales Chart', path: '/dashboard/chart', icon: 'insights' }
    ]
  },
  {
    title: 'สต็อกสินค้า',
    icon: 'stock',
    path: '/stock',
  }
];
function toggleLeftDrawer() {
  drawerOpen.value = !drawerOpen.value
}
</script>
