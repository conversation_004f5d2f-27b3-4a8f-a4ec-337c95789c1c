import "reflect-metadata";
import { DataSource } from "typeorm";
import { SupplierType } from "../Models/SupplierType";
import { Supplier } from "../Models/Supplier";
import { ProductGroup } from "../Models/ProductGroup";
import { Product } from "../Models/Product";
import { Stock } from "../Models/Stock";
import { Branch } from "../Models/Branch";
import { User } from "../Models/User";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { SpecialReportGroup } from "../Models/SpecialReportGroup";
import { Attendance } from "../Models/Attendance";
import { LeaveRequest } from "../Models/LeaveRequest";

export const AppDataSource = new DataSource({
  type: "better-sqlite3",
  database: "./database2.sqlite",
  synchronize: false, // ตั้งค่าเป็น false ก่อน
  logging: false,
  entities: [
    SupplierType,
    Supplier,
    ProductGroup,
    Product,
    Stock,
    Branch,
    User,
    PurchaseOrder,
    PurchaseOrderItem,
    SpecialReportGroup,
    Attendance,
    LeaveRequest,
  ],
});

AppDataSource.initialize()
  .then(async () => {
    console.log("✅ Connected to SQLite database");

    // // ลบตาราง supplier หากอยู่
    // await AppDataSource.query(`DROP TABLE IF EXISTS supplier;`);
    // await AppDataSource.query(`DROP TABLE IF EXISTS supplierType;`); // ลบ SupplierType หากจำเป็น

    try {
      // Check if the new columns exist, if not add them
      const tableInfo = await AppDataSource.query(`PRAGMA table_info(user);`);
      const columnNames = tableInfo.map((col: any) => col.name);

      if (!columnNames.includes("sick_leave_remaining")) {
        await AppDataSource.query(
          `ALTER TABLE user ADD COLUMN sick_leave_remaining INTEGER DEFAULT 0;`
        );
        console.log("✅ Added sick_leave_remaining column");
      }

      if (!columnNames.includes("personal_leave_remaining")) {
        await AppDataSource.query(
          `ALTER TABLE user ADD COLUMN personal_leave_remaining INTEGER DEFAULT 0;`
        );
        console.log("✅ Added personal_leave_remaining column");
      }

      console.log("✅ Database schema updated successfully");
    } catch (error) {
      console.error("❌ Error updating database schema:", error);
    }
  })
  .catch((error) => {
    console.error("❌ Error connecting to SQLite:", error);
  });
