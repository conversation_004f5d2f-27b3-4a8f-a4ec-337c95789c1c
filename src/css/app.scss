/* app global css */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai+Looped:wght@100;200;300;400;500;600;700&display=swap');
* {
  font-family: 'IBM Plex Sans Thai Looped', sans-serif;
}

body {
  background-color: #f3f3f3;
}
.container {
  background-color: white;
  border-radius: 10px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  max-width: 300px;
  height: 40px;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

/* Global faded styling for QDate components - dates from other months */
.faded-other-months :deep(.q-date__calendar-item--out) {
  opacity: 0.3;
}

.faded-other-months :deep(.q-date__calendar-item--out .q-btn) {
  color: #999 !important;
}

.faded-other-months :deep(.q-date__calendar-item--out:hover .q-btn) {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

$primary: #294888;
$green: #439e62;
$bg: #91d2c1;
$positive: #36b54d;
$negative: #b53638;
$orange: #ed9b53;
$blue: #83a7d8;
$main: #609fa3;
$dg: #e1edea;
$yellow: #DFAB4B;

// === Text Color Utilities ===
.text-primary {
  color: $primary !important;
}
.text-green {
  color: $green !important;
}
.text-bg {
  color: $bg !important;
}
.text-positive {
  color: $positive !important;
}
.text-negative {
  color: $negative !important;
}
.text-orange {
  color: $orange !important;
}
.text-blue {
  color: $blue !important;
}
.text-main {
  color: $main !important;
}
.text-dg {
  color: $dg !important;
}
.text-yellow {
  color: $yellow !important;
}

// === Background Color Utilities ===
.bg-primary {
  background-color: $primary !important;
}
.bg-green {
  background-color: $green !important;
}
.bg-bg {
  background-color: $bg !important;
}
.bg-positive {
  background-color: $positive !important;
}
.bg-negative {
  background-color: $negative !important;
}
.bg-orange {
  background-color: $orange !important;
}
.bg-blue {
  background-color: $blue !important;
}
.bg-main {
  background-color: $main !important;
}
.bg-dg {
  background-color: $dg !important;
}
.bg-yellow {
  background-color: $yellow !important;
}

// === Border Color Utilities ===
.border-primary {
  border-color: $primary !important;
}
.border-green {
  border-color: $green !important;
}
.border-bg {
  border-color: $bg !important;
}
.border-positive {
  border-color: $positive !important;
}
.border-negative {
  border-color: $negative !important;
}
.border-orange {
  border-color: $orange !important;
}
.border-blue {
  border-color: $blue !important;
}
.border-main {
  border-color: $main !important;
}
.border-dg {
  border-color: $dg !important;
}
