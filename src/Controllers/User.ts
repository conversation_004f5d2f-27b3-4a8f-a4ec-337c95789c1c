import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { User } from "../Models/User";
import fs from "fs";
import path from "path";
import multer from "multer";

export class UserController {
  // Configure multer for image uploads
  private upload = multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
      // Accept only image files
      if (file.mimetype.startsWith("image/")) {
        cb(null, true);
      } else {
        cb(new Error("Only image files are allowed!"));
      }
    },
  });

  private checkUserImageExists(userId: number): string | null {
    const imageFormats = [".png", ".jpeg", ".jpg"];
    const imagesDir = path.join(__dirname, "../../images/user");

    for (const format of imageFormats) {
      const imagePath = path.join(imagesDir, `user${userId}${format}`);
      if (fs.existsSync(imagePath)) {
        return `/images/user/user${userId}${format}`;
      }
    }

    return null;
  }

  private saveImageFile(
    userId: number,
    imageBuffer: Buffer,
    originalName: string
  ): string {
    const imagesDir = path.join(__dirname, "../../images/user");

    // Create directory if it doesn't exist
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }

    // Get file extension from original name
    const ext = path.extname(originalName).toLowerCase();
    const allowedExtensions = [".png", ".jpg", ".jpeg"];

    // Use .jpeg as default if extension is not allowed
    const fileExtension = allowedExtensions.includes(ext) ? ext : ".jpeg";

    // Create filename with user ID
    const filename = `user${userId}${fileExtension}`;
    const filePath = path.join(imagesDir, filename);

    // Remove any existing image files for this user first
    const existingImagePath = this.checkUserImageExists(userId);
    if (existingImagePath) {
      const existingFilePath = path.join(__dirname, "../..", existingImagePath);
      if (fs.existsSync(existingFilePath)) {
        fs.unlinkSync(existingFilePath);
      }
    }

    // Save the new image
    fs.writeFileSync(filePath, imageBuffer);

    return `/images/user/${filename}`;
  }

  private processBase64Image(
    userId: number,
    base64Data: string
  ): string | null {
    try {
      // Extract the base64 data and mime type
      const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        throw new Error("Invalid base64 format");
      }

      const mimeType = matches[1];
      const base64Content = matches[2];

      // Check if it's an image
      if (!mimeType.startsWith("image/")) {
        throw new Error("File is not an image");
      }

      // Convert base64 to buffer
      const imageBuffer = Buffer.from(base64Content, "base64");

      // Determine file extension from mime type
      let extension = ".jpeg"; // default
      if (mimeType === "image/png") extension = ".png";
      else if (mimeType === "image/jpeg" || mimeType === "image/jpg")
        extension = ".jpeg";

      // Save the image
      return this.saveImageFile(userId, imageBuffer, `image${extension}`);
    } catch (error) {
      console.error("Error processing base64 image:", error);
      return null;
    }
  }

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const userRepository = AppDataSource.getRepository(User);
      const users = await userRepository.find({ relations: ["branch"] });

      // Augment users with correct image paths
      const augmentedUsers = users.map((user) => {
        const localImagePath = this.checkUserImageExists(user.id);
        return {
          ...user,
          image: localImagePath || user.image,
        };
      });

      res.status(200).json(augmentedUsers);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const userRepository = AppDataSource.getRepository(User);
      const user = await userRepository.findOne({
        where: { id: Number(id) },
        relations: ["branch"],
      });

      if (user) {
        // Check for local image file
        const localImagePath = this.checkUserImageExists(user.id);
        if (localImagePath) {
          user.image = localImagePath;
        }

        res.status(200).json(user);
      } else {
        res.status(404).json({ message: "User not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);

    try {
      const userData = { ...req.body };

      // Handle base64 image if provided
      if (
        userData.image &&
        typeof userData.image === "string" &&
        userData.image.startsWith("data:image/")
      ) {
        // This is a base64 image, we'll process it after user creation
        const base64Image = userData.image;
        userData.image = "noimage.png"; // Set default for now

        // Create and save user first
        const newUser = userRepository.create(userData);
        const saveResult = await userRepository.save(newUser);
        const savedUser = Array.isArray(saveResult)
          ? saveResult[0]
          : saveResult;

        // Now process the base64 image with the user's ID
        const imagePath = this.processBase64Image(savedUser.id, base64Image);
        if (imagePath) {
          savedUser.image = imagePath;
          const updateResult = await userRepository.save(savedUser);
          const updatedUser = Array.isArray(updateResult)
            ? updateResult[0]
            : updateResult;
          res.status(201).json(updatedUser);
        } else {
          res.status(201).json(savedUser);
        }
      } else {
        // No image or regular image path
        const newUser = userRepository.create(userData);
        const saveResult = await userRepository.save(newUser);
        const savedUser = Array.isArray(saveResult)
          ? saveResult[0]
          : saveResult;
        res.status(201).json(savedUser);
      }
    } catch (error) {
      console.error("Error creating user:", error);
      res.status(400).json({ message: "Error creating user" });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const userRepository = AppDataSource.getRepository(User);

    try {
      const user = await userRepository.findOne({ where: { id: Number(id) } });
      if (user) {
        userRepository.merge(user, req.body);
        const updatedUser = await userRepository.save(user);
        res.status(200).json(updatedUser);
      } else {
        res.status(404).json({ message: "User not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating user" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const userRepository = AppDataSource.getRepository(User);

    try {
      const result = await userRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "User not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getUserImages(req: Request, res: Response): Promise<void> {
    try {
      const imagesDir = path.join(__dirname, "../../images/user");
      console.log("Images directory:", imagesDir);
      console.log("Directory exists:", fs.existsSync(imagesDir));

      if (fs.existsSync(imagesDir)) {
        console.log("Directory contents:", fs.readdirSync(imagesDir));
      }

      // Check if directory exists
      if (!fs.existsSync(imagesDir)) {
        res.status(404).json({ message: "Images directory not found" });
        return;
      }

      // Add this to check for a specific file
      const specificFile = path.join(imagesDir, "user1.jpeg");
      console.log("Specific file exists:", fs.existsSync(specificFile));

      // Read all files in the directory
      const files = fs.readdirSync(imagesDir);

      // Filter for user image files (matching pattern user{id}.{extension})
      const userImageFiles = files.filter((file) =>
        /^user\d+\.(png|jpg|jpeg)$/i.test(file)
      );

      // Map to more useful format with user IDs
      const userImages = userImageFiles.map((file) => {
        const match = file.match(/^user(\d+)/i);
        const userId = match ? parseInt(match[1]) : null;
        return {
          fileName: file,
          userId: userId,
          path: `/images/user/${file}`,
        };
      });

      res.status(200).json({
        totalImages: userImages.length,
        images: userImages,
      });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async verifyUserImages(req: Request, res: Response): Promise<void> {
    try {
      const userRepository = AppDataSource.getRepository(User);
      const users = await userRepository.find();
      const imagesDir = path.join(__dirname, "../../images/user");

      if (!fs.existsSync(imagesDir)) {
        res.status(404).json({ message: "Images directory not found" });
        return;
      }

      const files = fs.readdirSync(imagesDir);

      // Create a verification report for each user
      const verificationResults = users.map((user) => {
        const expectedImagePatterns = [
          `user${user.id}.png`,
          `user${user.id}.jpg`,
          `user${user.id}.jpeg`,
        ];

        // Find matching image files for this user
        const matchingFiles = files.filter((file) =>
          expectedImagePatterns.includes(file.toLowerCase())
        );

        // Check if the image in the database matches any of the found files
        const dbImageFilename = user.image.split("/").pop(); // Extract filename from path
        const dbImageMatches = dbImageFilename
          ? matchingFiles.includes(dbImageFilename)
          : false;

        return {
          userId: user.id,
          userName: user.name,
          dbImagePath: user.image,
          localImagesFound: matchingFiles,
          hasMatchingLocalImage: matchingFiles.length > 0,
          dbImageMatchesLocal: dbImageMatches,
        };
      });

      res.status(200).json({
        totalUsers: users.length,
        verificationResults,
      });
    } catch (error) {
      console.error("Error verifying user images:", error);
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter } = req.body;

    try {
      const userRepository = AppDataSource.getRepository(User);

      const query = userRepository
        .createQueryBuilder("user")
        .leftJoinAndSelect("user.branch", "branch")
        .where("1=1");

      if (search) {
        query.andWhere(
          `(
            user.name LIKE :search OR
            user.tel LIKE :search OR
            user.role LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`user.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      const users = await query.getMany();

      // Augment users with correct image paths
      const augmentedUsers = users.map((user) => {
        const localImagePath = this.checkUserImageExists(user.id);
        return {
          ...user,
          image: localImagePath || user.image,
        };
      });

      res.status(200).json(augmentedUsers);
    } catch (error) {
      console.error("Error filtering users:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getUserImageById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const userRepository = AppDataSource.getRepository(User);
      const user = await userRepository.findOne({ where: { id: Number(id) } });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // Check for local image file first
      const localImagePath = this.checkUserImageExists(user.id);

      if (localImagePath) {
        // Return the local image path if found
        res.status(200).json({
          userId: user.id,
          userName: user.name,
          imagePath: localImagePath,
        });
      } else if (user.image) {
        // Return the database image path if no local image found
        res.status(200).json({
          userId: user.id,
          userName: user.name,
          imagePath: user.image,
        });
      } else {
        // No image found
        res.status(404).json({
          userId: user.id,
          userName: user.name,
          message: "No image found for this user",
        });
      }
    } catch (error) {
      console.error("Error getting user image:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public uploadImage = this.upload.single("image");

  public async uploadUserImage(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.body;
      const file = req.file;

      if (!userId) {
        res.status(400).json({ message: "User ID is required" });
        return;
      }

      if (!file) {
        res.status(400).json({ message: "No image file provided" });
        return;
      }

      // Check if user exists
      const userRepository = AppDataSource.getRepository(User);
      const user = await userRepository.findOne({
        where: { id: Number(userId) },
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // Save the image file
      const imagePath = this.saveImageFile(
        Number(userId),
        file.buffer,
        file.originalname
      );

      // Update user's image path in database
      user.image = imagePath;
      await userRepository.save(user);

      res.status(200).json({
        message: "Image uploaded successfully",
        userId: Number(userId),
        imagePath: imagePath,
        success: true,
      });
    } catch (error) {
      console.error("Error uploading user image:", error);
      res.status(500).json({
        message: "Server error during image upload",
        error: error instanceof Error ? error.message : "Unknown error",
        success: false,
      });
    }
  }

  public async getUserCount(req: Request, res: Response): Promise<void> {
    try {
      const userRepository = AppDataSource.getRepository(User);
      const userCount = await userRepository.count();
      res.status(200).json({ userCount });
    } catch (error) {
      console.error("Error getting user count:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }
}
