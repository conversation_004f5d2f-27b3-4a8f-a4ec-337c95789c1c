import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { Product } from "../Models/Product";
import { In } from "typeorm";

export class PurchaseOrderItemController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
      const purchaseOrderItems = await purchaseOrderItemRepository.find({ relations: ["purchase_order", "product"] });
      res.status(200).json(purchaseOrderItems);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
      const purchaseOrderItem = await purchaseOrderItemRepository.findOne({
        where: { id: Number(id) },
        relations: ["purchase_order", "product"]
      });
      if (purchaseOrderItem) {
        res.status(200).json(purchaseOrderItem);
      } else {
        res.status(404).json({ message: "Purchase Order Item not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
  // public async create(req: Request, res: Response): Promise<void> {
  //   const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
  //   const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
  //   const productRepository = AppDataSource.getRepository(Product);
  //   try {
  //     const { purchase_order_id, product_id, quantity, unit_price } = req.body;

  //     const purchaseOrder = await purchaseOrderRepository.findOne({ where: { id: purchase_order_id } });
  //     if (!purchaseOrder) {
  //       res.status(404).json({ message: "Purchase Order not found" });
  //       return;
  //     }

  //     const product = await productRepository.findOne({ where: { id: product_id } });
  //     if (!product) {
  //       res.status(404).json({ message: "Product not found" });
  //       return;
  //     }

  //     const total_price = quantity * unit_price;

  //     const newPurchaseOrderItem = purchaseOrderItemRepository.create({
  //       purchase_order: purchaseOrder,
  //       product: product,
  //       quantity: quantity,
  //       unit_price: unit_price,
  //       total_price: total_price,
  //     });
  //     const savedPurchaseOrderItem = await purchaseOrderItemRepository.save(newPurchaseOrderItem);

  //     res.status(201).json({ message: "Purchase Order Item created successfully", purchaseOrderItem: savedPurchaseOrderItem });
  //   } catch (error) {
  //     console.error("Error creating Purchase Order Item:", error);
  //     res.status(400).json({ message: "Error creating Purchase Order Item", error });
  //   }
  // } 
  public async create(req: Request, res: Response): Promise<void> {
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
    const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
    const productRepository = AppDataSource.getRepository(Product);

    try {
      const purchaseOrderId = parseInt(req.params.orderId, 10);
      if (isNaN(purchaseOrderId)) {
        res.status(400).json({ message: "Invalid orderId" });
        return;
      }

      const purchaseOrderItems = req.body;
      if (!Array.isArray(purchaseOrderItems)) {
        res.status(400).json({ message: "Invalid request body" });
        return;
      }

      const purchaseOrder = await purchaseOrderRepository.findOne({
        where: { id: purchaseOrderId }
      });

      if (!purchaseOrder) {
        res.status(404).json({ message: "Purchase Order not found" });
        return;
      }

      // ✅ ดึงข้อมูล Order Items ที่มีอยู่เดิม
      const existingItems = await purchaseOrderItemRepository.find({
        where: { purchase_order: { id: purchaseOrderId } }
      });

      // ✅ ดึงข้อมูลสินค้า
      const productIds = purchaseOrderItems
        .filter(item => typeof item.product === 'object' || typeof item.product_id === 'number')
        .map(item => (typeof item.product === 'object' ? item.product.id : item.product_id));

      const products = await productRepository.find({ where: productIds.map(id => ({ id })) });

      // ✅ เตรียมรายการใหม่ (เพิ่ม + อัปเดต)
      const updatedItems = purchaseOrderItems.map(item => {
        let product: Product | undefined;

        if (typeof item.product === 'object') {
          product = products.find(p => p.id === item.product.id);
        } else if (typeof item.product_id === 'number') {
          product = products.find(p => p.id === item.product_id);
        }

        if (!product) return null;

        if (item.id === 0) {
          return purchaseOrderItemRepository.create({
            purchase_order: purchaseOrder,
            product,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.quantity * item.unit_price,
          });
        } else {
          const existingItem = existingItems.find(i => i.id === item.id);
          if (existingItem) {
            existingItem.quantity = item.quantity;
            existingItem.unit_price = item.unit_price;
            existingItem.total_price = item.quantity * item.unit_price;
            return existingItem;
          }
        }
        return null;
      }).filter((item): item is PurchaseOrderItem => item !== null);

      // ✅ ลบ Order Items ที่ไม่มีอยู่ในรายการใหม่
      const receivedItemIds = purchaseOrderItems.map(item => item.id);
      const itemsToDelete = existingItems.filter(item => !receivedItemIds.includes(item.id));

      if (itemsToDelete.length > 0) {
        await purchaseOrderItemRepository.remove(itemsToDelete);
      }

      // ✅ ป้องกัน `save([])`
      if (updatedItems.length > 0) {
        const savedItems = await purchaseOrderItemRepository.save(updatedItems);

        // 🔥 ✅ คำนวณค่า po_total & order_total ใหม่
        const updatedOrderItems = await purchaseOrderItemRepository.find({
          where: { purchase_order: { id: purchaseOrderId } }
        });

        const poTotal = updatedOrderItems.reduce((sum, item) => sum + item.total_price, 0);
        const orderTotal = updatedOrderItems.reduce((sum, item) => sum + item.quantity, 0);

        // 🔥 ✅ อัปเดตค่ารวมใน Purchase Order
        purchaseOrder.po_total = poTotal;
        purchaseOrder.order_total = orderTotal;
        await purchaseOrderRepository.save(purchaseOrder);

        res.status(201).json({
          message: "Purchase Order Items updated successfully",
          purchaseOrderItems: savedItems,
          purchaseOrder: {
            po_total: purchaseOrder.po_total,
            order_total: purchaseOrder.order_total
          }
        });
      } else {
        res.status(200).json({
          message: "No updates were made",
          purchaseOrderItems: [],
          purchaseOrder: {
            po_total: purchaseOrder.po_total,
            order_total: purchaseOrder.order_total
          }
        });
      }

    } catch (error) {
      console.error("Error updating Purchase Order Items:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }






  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
    try {
      const purchaseOrderItem = await purchaseOrderItemRepository.findOne({ where: { id: Number(id) } });
      if (purchaseOrderItem) {
        purchaseOrderItemRepository.merge(purchaseOrderItem, req.body);
        purchaseOrderItem.total_price = purchaseOrderItem.quantity * purchaseOrderItem.unit_price;
        const updatedPurchaseOrderItem = await purchaseOrderItemRepository.save(purchaseOrderItem);
        res.status(200).json(updatedPurchaseOrderItem);
      } else {
        res.status(404).json({ message: "Purchase Order Item not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Purchase Order Item" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
    try {
      const result = await purchaseOrderItemRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Purchase Order Item not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async updateMultiple(req: Request, res: Response): Promise<void> {
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);

    try {
      const purchase_order_id = Number(req.params.id);
      console.log("Received purchase_order_id:", purchase_order_id);
      console.log("Received body:", req.body);

      // ถ้า req.body เป็น array ให้ใช้ได้เลย, ถ้าเป็น object ให้ดึง items
      const items = Array.isArray(req.body) ? req.body : req.body.items;

      if (!items || !Array.isArray(items) || items.length === 0) {
        res.status(400).json({ message: "Invalid request data" });
        return;
      }

      // หา PurchaseOrderItem ทั้งหมดที่ต้องอัปเดต
      const itemIds = items.map((item) => item.id);
      const existingItems = await purchaseOrderItemRepository.findBy({ id: In(itemIds) });
      if (existingItems.length !== items.length) {
        res.status(404).json({ message: "Some Purchase Order Items not found" });
        return;
      }

      // อัปเดตค่าใหม่
      const updatedItems = existingItems.map((existingItem) => {
        const newItem = items.find((item) => item.id === existingItem.id);
        if (newItem) {
          return {
            ...existingItem,
            quantity: newItem.quantity,
            unit_price: newItem.unit_price,
            total_price: newItem.quantity * newItem.unit_price,
          };
        }
        return existingItem;
      });

      // บันทึกการอัปเดตทั้งหมด
      await purchaseOrderItemRepository.save(updatedItems);

      res.status(200).json({ message: "Purchase Order Items updated successfully", updatedItems });
    } catch (error) {
      res.status(500).json({ message: "Error updating Purchase Order Items", error });
    }
  }



  public async deleteMultiple(req: Request, res: Response): Promise<void> {
    console.log("🔹 DELETE API CALLED");
    console.log("🔹 Received request body:", req.body);

    const { ids } = req.body;
    console.log("🔹 Extracted IDs:", ids);

    // const { ids } = req.body; // รับ List ของ PurchaseOrderItem IDs ที่ต้องการลบ
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);

    try {
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        res.status(400).json({ message: "Invalid request data" });
        return;
      }

      const result = await purchaseOrderItemRepository.delete(ids);
      console.log("Delete result:", result);

      console.log("Received IDs for deletion:", ids);
      const itemsToDelete = await purchaseOrderItemRepository.findBy({ id: In(ids) });
      console.log("Items found in DB:", itemsToDelete);

      if (itemsToDelete.length === 0) {
        res.status(404).json({ message: "Some Purchase Order Items not found" });
        return;
      }

      await purchaseOrderItemRepository.remove(itemsToDelete);
      res.status(204).send();

    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

}
