import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { User } from "../Models/User";
import * as bcrypt from "bcrypt";
import { generateToken, verifyToken } from "../utils/jwt";

export class AuthController {
  public async login(req: Request, res: Response): Promise<void> {
    const { name, password } = req.body;

    try {
      const userRepository = AppDataSource.getRepository(User);

      // ค้นหาผู้ใช้จากชื่อ
      const user = await userRepository.findOne({
        where: { name },
        relations: ["branch"],
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // เปรียบเทียบรหัสผ่านโดยใช้ bcrypt
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        res.status(400).json({ message: "Invalid credentials" });
        return;
      }

      // ถ้ารหัสผ่านตรง สร้าง JWT token และส่งข้อมูลผู้ใช้กลับ
      try {
        const token = generateToken({
          id: user.id,
          name: user.name,
          role: user.role,
        });

        const tokenExpiration = new Date(
          Date.now() + 4 * 60 * 60 * 1000
        ).toISOString();
        const { password: _, ...userWithoutPassword } = user;

        console.log("🔍 Backend login response:", {
          userId: user.id,
          userName: user.name,
          tokenExpiration,
          tokenLength: token.length,
        });

        res.status(200).json({
          ...userWithoutPassword,
          token,
          tokenExpiration,
        });
      } catch (jwtError) {
        console.error("❌ JWT Error:", jwtError);
        // Fallback without JWT for now
        const { password: _, ...userWithoutPassword } = user;
        res.status(200).json({
          ...userWithoutPassword,
          token: "temp-token-" + Date.now(),
          tokenExpiration: new Date(
            Date.now() + 4 * 60 * 60 * 1000
          ).toISOString(),
        });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  /**
   * Refresh JWT token
   */
  public async refreshToken(req: Request, res: Response): Promise<void> {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      res.status(401).json({ message: "Token required" });
      return;
    }

    try {
      const decoded = verifyToken(token);
      const userRepository = AppDataSource.getRepository(User);

      // Verify user still exists
      const user = await userRepository.findOne({
        where: { id: decoded.userId },
        relations: ["branch"],
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      // Generate new token
      const newToken = generateToken({
        id: user.id,
        name: user.name,
        role: user.role,
      });

      res.status(200).json({
        token: newToken,
        tokenExpiration: new Date(
          Date.now() + 4 * 60 * 60 * 1000
        ).toISOString(),
      });
    } catch (error) {
      res.status(403).json({ message: "Invalid or expired token" });
    }
  }

  /**
   * Validate current token and return user info
   */
  public async validateToken(req: Request, res: Response): Promise<void> {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      res.status(401).json({ message: "Token required" });
      return;
    }

    try {
      const decoded = verifyToken(token);
      const userRepository = AppDataSource.getRepository(User);

      // Get current user data
      const user = await userRepository.findOne({
        where: { id: decoded.userId },
        relations: ["branch"],
      });

      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      const { password: _, ...userWithoutPassword } = user;

      res.status(200).json({
        ...userWithoutPassword,
        tokenValid: true,
      });
    } catch (error) {
      res.status(403).json({
        message: "Invalid or expired token",
        tokenValid: false,
      });
    }
  }
}
