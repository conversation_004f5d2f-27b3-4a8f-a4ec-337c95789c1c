import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { Branch } from "../Models/Branch";

export class BranchController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const branchRepository = AppDataSource.getRepository(Branch);
      const branches = await branchRepository.find();
      res.status(200).json(branches);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const branchRepository = AppDataSource.getRepository(Branch);
      const branch = await branchRepository.findOne({ where: { id: Number(id) } });

      if (branch) {
        res.status(200).json(branch);
      } else {
        res.status(404).json({ message: "Branch not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const branchRepository = AppDataSource.getRepository(Branch);

    try {
      const newBranch = branchRepository.create(req.body);
      const savedBranch = await branchRepository.save(newBranch);
      res.status(201).json(savedBranch);
    } catch (error) {
      res.status(400).json({ message: "Error creating branch" });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const branchRepository = AppDataSource.getRepository(Branch);

    try {
      const branch = await branchRepository.findOne({ where: { id: Number(id) } });
      if (branch) {
        branchRepository.merge(branch, req.body);
        const updatedBranch = await branchRepository.save(branch);
        res.status(200).json(updatedBranch);
      } else {
        res.status(404).json({ message: "Branch not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating branch" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const branchRepository = AppDataSource.getRepository(Branch);

    try {
      const result = await branchRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Branch not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}
