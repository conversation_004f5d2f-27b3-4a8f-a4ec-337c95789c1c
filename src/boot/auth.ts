import { defineBoot } from '#q-app/wrappers'
import { useAuthStore } from 'src/stores/authStore'

export default defineBoot(() => {
  // Initialize auth store
  const authStore = useAuthStore()

  console.log('🔐 Auth boot file loading...')

  // Initialize authentication state from localStorage
  authStore.initializeAuth()

  // Setup automatic token refresh
  authStore.setupTokenRefresh()

  console.log('🔐 Auth boot file loaded - authentication system initialized')
  console.log('🔍 Auth state after boot:', {
    isAuthenticated: authStore.isAuthenticated,
    hasUser: !!authStore.currentUser,
    userName: authStore.currentUser?.name,
  })
})
