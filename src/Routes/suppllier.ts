import { Router } from "express";
import { SupplierController } from "../Controllers/supplier";

const router = Router();
const controller = new SupplierController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));

export default router;