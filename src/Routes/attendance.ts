import { Router } from "express";
import { AttendanceController } from "../Controllers/Attendance";

const router = Router();
const controller = new AttendanceController();

router.post("/clock-in", (req, res) => controller.clockIn(req, res));
router.post("/clock-out", (req, res) => controller.clockOut(req, res));
router.get("/user/:userId", (req, res) =>
  controller.getUserAttendance(req, res)
);
router.get("/status", (req, res) =>
  controller.getAllAttendanceStatus(req, res)
);
router.get("/summary-by-role", (req, res) =>
  controller.getAttendanceSummaryByRole(req, res)
);
router.get("/total-by-role", (req, res) =>
  controller.getTotalAttendanceByRole(req, res)
);
router.get("/total-by-category", (req, res) =>
  controller.getTotalAttendanceByCategory(req, res)
);
router.get("/today/:userId", (req, res) =>
  controller.getAttendanceUserTodayById(req, res)
);
router.get("/date-range", (req, res) =>
  controller.getAttendanceByDateRange(req, res)
);
router.post("/date-range", (req, res) =>
  controller.getAttendanceByDateRange(req, res)
);
router.get("/average-work-time", (req, res) =>
  controller.getAverageWorkTime(req, res)
);
router.get("/monthly-hours", (req, res) =>
  controller.getMonthlyHoursByDateRange(req, res)
);
router.post("/monthly-hours", (req, res) =>
  controller.getMonthlyHoursByDateRange(req, res)
);

export default router;
