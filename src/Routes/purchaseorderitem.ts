import { Router } from "express";
import { PurchaseOrderItemController } from "../Controllers/purchaseorderitem";

const router = Router();
const controller = new PurchaseOrderItemController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/:orderId", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.put("/:id/updateMultiple", controller.updateMultiple.bind(controller));
router.delete("/:ids", controller.deleteMultiple.bind(controller));
export default router;
