import { Router } from "express";
import { ProductController } from "../Controllers/product";

const router = Router();
const controller = new ProductController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));
router.post("/bulk", controller.createBulk.bind(controller));


export default router;
