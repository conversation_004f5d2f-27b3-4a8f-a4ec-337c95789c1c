import type { GoodsRecript } from "./goodsReceipt";
import type { Supplier } from "./supplier";
import type { user } from "./user";

export interface PaymentGoodsReceipt{
  id: number;
  code: string;
  total_net:number;
  paid_amount: number;
  unpaid_amount: number;
  change_amount: number;
  payment_type: string;
  payment_date: Date;
  cash_amount: number;
  total_amount: number;
  note: string;
  receivedBy: Supplier;
  user: user;
  gr: GoodsRecript;

}
