import type { BranchReceive } from "./branchReceive";
import type { Product } from "./product";
import type { StockTransferOrderDetails } from "./stockTransferOrderDetails";
import type { StockTransferSlip } from "./stockTransferSlip";

export interface StockTransferSlipDetails {
  id: number;
  sts: StockTransferSlip;
  sto_details: StockTransferOrderDetails;
  product: Product;
  lot_number?: string;
  sto_quantity: number;
  sts_quantity: string;
  sts_details_status: string;
  br: BranchReceive[];
}

