import type { GoodsReceiptDetail } from "./goodsReceiptDatail";
import type { PaymentGoodsReceipt } from "./paymentGoodsReceipt";
import type { PurchaseOrder } from "./purchaseOrder"
import type { Supplier } from "./supplier"
import type { user } from "./user";

export interface GoodsReceipt{
  id: number;
  code: string;
  po: PurchaseOrder;
  distributor: Supplier;
  po_date: Date;
  po_code: string;
  date_document: Date;
  receive_date: Date;
  po_total: number;
  tax: number;
  tax_total: number;
  gr_total: number;
  tax_invoice_number: string;
  status: string;
  user: user;
  product_price_tax: string;
  order_discount_tax: string;
  note: string;
  gr_detail: GoodsReceiptDetail[];
  payment: PaymentGoodsReceipt[]; 


}
