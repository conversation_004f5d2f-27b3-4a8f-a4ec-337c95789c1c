import { api } from 'src/boot/axios';

export class BranchService {
    static path = 'branch';

    static async getAll() {
        const res = await api.get(this.path);
        console.log('Call From Services')
        console.log(res.data)
        return res.data;
    }

    static async getOne(id: number) {
        const res = await api.get(`${this.path}/${id}`);
        return res.data;
    }

}

