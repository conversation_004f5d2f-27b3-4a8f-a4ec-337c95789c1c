import axios from 'axios'
import type { Product } from 'src/types/product'
import type { ProductGroup } from 'src/types/product-group'
import type { SpecialReportGroup } from 'src/types/special-report-group'

const API_URL = 'http://localhost:3000/product'
const API_ProductGroup = 'http://localhost:3000/productgroup'
const API_SpecialReportGroup = 'http://localhost:3000/specialreportgroup'

export const productService = {
  async getProducts(): Promise<Product[]> {
    const response = await axios.get(API_URL)
    console.log('Loading data :', response.data)
    return response.data
  },

  async deleteProduct(id: number): Promise<void> {
    await axios.delete(`${API_URL}/${id}`)
  },

  async addProduct(product: Product): Promise<Product> {
    const response = await axios.post(API_URL, product)
    return response.data
  },

  async updateProduct(product: Product): Promise<Product> {
    const response = await axios.put(`${API_URL}/${product.id}`, product)
    return response.data
  },
  async filterProducts(search: string, filter?: string): Promise<Product[]> {
    const response = await axios.post(`${API_URL}/filter`, {
      search,
      filter,
    })
    return response.data
  },
}

export const productGroupService = {
  async getProductGroups(): Promise<ProductGroup[]> {
    const response = await axios.get(API_ProductGroup)
    console.log('Loading productGroup :', response.data)
    return response.data
  },
}

export const specialReportGroupService = {
  async getSpecialReportGroup(): Promise<SpecialReportGroup[]> {
    const response = await axios.get(API_SpecialReportGroup)
    console.log('Loading SpecialProductGroup :', response.data)
    return response.data
  },
}
