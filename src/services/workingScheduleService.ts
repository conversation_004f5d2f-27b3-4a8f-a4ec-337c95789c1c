import { api } from 'src/boot/axios'
import type { 
  WorkingSchedule, 
  WorkingScheduleFormData, 
  WorkingScheduleApiResponse 
} from 'src/types/workingSchedule'

export class WorkingScheduleService {
  static path = 'working-schedule'

  /**
   * Get working schedule for a specific user
   * @param userId - The user ID to get schedule for
   * @returns Promise<WorkingSchedule | null>
   */
  static async getUserSchedule(userId: number): Promise<WorkingSchedule | null> {
    try {
      const response = await api.get<WorkingScheduleApiResponse>(`${this.path}/user/${userId}`)
      return this.formatScheduleResponse(response.data)
    } catch (error: any) {
      if (error.response?.status === 404) {
        // User doesn't have a schedule yet
        return null
      }
      console.error('Error fetching user schedule:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch user schedule')
    }
  }

  /**
   * Create or update a working schedule
   * @param scheduleData - The schedule data to save
   * @returns Promise<WorkingSchedule>
   */
  static async createOrUpdateSchedule(scheduleData: WorkingScheduleFormData): Promise<WorkingSchedule> {
    try {
      const response = await api.post<WorkingScheduleApiResponse>(this.path, scheduleData)
      return this.formatScheduleResponse(response.data)
    } catch (error: any) {
      console.error('Error creating/updating schedule:', error)
      throw new Error(error.response?.data?.message || 'Failed to save schedule')
    }
  }

  /**
   * Update an existing working schedule
   * @param scheduleData - The schedule data to update
   * @returns Promise<WorkingSchedule>
   */
  static async updateSchedule(scheduleData: WorkingScheduleFormData): Promise<WorkingSchedule> {
    try {
      const response = await api.put<WorkingScheduleApiResponse>(this.path, scheduleData)
      return this.formatScheduleResponse(response.data)
    } catch (error: any) {
      console.error('Error updating schedule:', error)
      throw new Error(error.response?.data?.message || 'Failed to update schedule')
    }
  }

  /**
   * Get all working schedules
   * @returns Promise<WorkingSchedule[]>
   */
  static async getAllSchedules(): Promise<WorkingSchedule[]> {
    try {
      const response = await api.get<WorkingScheduleApiResponse[]>(this.path)
      return response.data.map(schedule => this.formatScheduleResponse(schedule))
    } catch (error: any) {
      console.error('Error fetching all schedules:', error)
      throw new Error(error.response?.data?.message || 'Failed to fetch schedules')
    }
  }

  /**
   * Delete a working schedule
   * @param scheduleId - The schedule ID to delete
   * @returns Promise<void>
   */
  static async deleteSchedule(scheduleId: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${scheduleId}`)
    } catch (error: any) {
      console.error('Error deleting schedule:', error)
      throw new Error(error.response?.data?.message || 'Failed to delete schedule')
    }
  }

  /**
   * Check if a user has a working schedule
   * @param userId - The user ID to check
   * @returns Promise<boolean>
   */
  static async hasSchedule(userId: number): Promise<boolean> {
    try {
      const schedule = await this.getUserSchedule(userId)
      return schedule !== null
    } catch (error) {
      return false
    }
  }

  /**
   * Create a default schedule for a user
   * @param userId - The user ID to create schedule for
   * @param checkInTime - Default check-in time (default: '09:00')
   * @param checkOutTime - Default check-out time (default: '17:00')
   * @returns Promise<WorkingSchedule>
   */
  static async createDefaultSchedule(
    userId: number, 
    checkInTime: string = '09:00', 
    checkOutTime: string = '17:00'
  ): Promise<WorkingSchedule> {
    const defaultScheduleData: WorkingScheduleFormData = {
      userId,
      standard_check_in_time: `${checkInTime}:00`,
      standard_check_out_time: `${checkOutTime}:00`,
      late_threshold_minutes: 5,
      early_threshold_minutes: 30,
    }

    return this.createOrUpdateSchedule(defaultScheduleData)
  }

  /**
   * Format API response to frontend format
   * @param apiResponse - Raw API response
   * @returns WorkingSchedule
   */
  private static formatScheduleResponse(apiResponse: WorkingScheduleApiResponse): WorkingSchedule {
    return {
      id: apiResponse.id,
      userId: apiResponse.user.id,
      userName: apiResponse.user.name,
      standard_check_in_time: apiResponse.standard_check_in_time,
      standard_check_out_time: apiResponse.standard_check_out_time,
      late_threshold_minutes: apiResponse.late_threshold_minutes,
      early_threshold_minutes: apiResponse.early_threshold_minutes,
      is_active: apiResponse.is_active,
      created_at: apiResponse.created_at,
      updated_at: apiResponse.updated_at,
      user: apiResponse.user,
    }
  }

  /**
   * Format time from HH:mm:ss to HH:mm
   * @param time - Time string in HH:mm:ss format
   * @returns Time string in HH:mm format
   */
  static formatTimeForDisplay(time: string): string {
    if (!time) return '00:00'
    return time.substring(0, 5) // Remove seconds
  }

  /**
   * Format time from HH:mm to HH:mm:ss for API
   * @param time - Time string in HH:mm format
   * @returns Time string in HH:mm:ss format
   */
  static formatTimeForAPI(time: string): string {
    if (!time) return '00:00:00'
    return time.includes(':') && time.split(':').length === 2 ? `${time}:00` : time
  }

  /**
   * Validate time format
   * @param time - Time string to validate
   * @returns boolean
   */
  static isValidTime(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    return timeRegex.test(time)
  }

  /**
   * Check if check-out time is after check-in time
   * @param checkInTime - Check-in time in HH:mm format
   * @param checkOutTime - Check-out time in HH:mm format
   * @returns boolean
   */
  static isValidTimeOrder(checkInTime: string, checkOutTime: string): boolean {
    if (!this.isValidTime(checkInTime) || !this.isValidTime(checkOutTime)) {
      return false
    }

    const [checkInHour, checkInMinute] = checkInTime.split(':').map(Number)
    const [checkOutHour, checkOutMinute] = checkOutTime.split(':').map(Number)

    const checkInMinutes = checkInHour * 60 + checkInMinute
    const checkOutMinutes = checkOutHour * 60 + checkOutMinute

    return checkOutMinutes > checkInMinutes
  }
}
