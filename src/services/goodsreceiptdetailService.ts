import axios from 'axios'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
// import type { GoodsReceiptDetail } from 'src/types/goodsReceiptDatail'

const API_URL = 'http://localhost:3000/gr-details'
// เปลี่ยนให้ตรงกับ backend ของคุณ

export const goodsReceiptDetailService = {
  // ดึงข้อมูลรายการ GoodsReceipt ทั้งหมด
  async getAll(): Promise<GoodsReceipt[]> {
    const response = await axios.get(API_URL)
    return response.data
  },


  // ดึงข้อมูล Purchase Order ตาม ID
  async getById(id: number): Promise<GoodsReceipt> {
    const response = await axios.get(`${API_URL}/${id}`)
    return response.data
  },


  async getProductByDistrbutor(id: number): Promise<GoodsReceipt[]> {
    const response = await axios.get(`${API_URL}/${id}/gr-details`)
    return response.data
  },
  async deletePoItems(selectedIds: number[]) {
    console.log(selectedIds)
    await axios.delete(`${API_URL2}/${selectedIds.join(',')}`, { data: { ids: selectedIds } })
  },

  // สร้าง Purchase Order ใหม่
  async createFromDistributor(gr: GoodsReceipt): Promise<GoodsReceipt> {
    const payload = {
      ...gr,
      date_document: gr.date_document.toISOString(), // 💡 แปลงเป็น string ISO ตอนส่งเท่านั้น
    }
    const response = await axios.post(API_URL, payload)
    return response.data
  },

  async createFromPO(POId: number): Promise<GoodsReceipt> {
    const response = await axios.post(`${API_URL}/createByPO/${POId}`)
    return response.data
  },

  // อัปเดต Purchase Order
  async update(id: number, gr: Partial<GoodsReceipt>): Promise<GoodsReceipt> {
    const response = await axios.put(`${API_URL}/${id}`, gr)
    return response.data
  },

  // ลบ Purchase Order
  async delete(id: number): Promise<void> {
    await axios.delete(`${API_URL}/${id}`)
  },

  async filter(
    search: string,
    filter: string,
    startDate: string,
    endDate: string,
  ): Promise<GoodsReceipt[]> {
    try {
      const response = await axios.post(`${API_URL}/filter`, {
        search,
        filter,
        startDate,
        endDate,
      })
      return response.data
    } catch (error) {
      console.error('Error filtering purchase orders', error)
      throw error
    }
  },
}
