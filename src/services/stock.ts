// import type { PageParams } from 'src/types/pagination';
import { api } from 'src/boot/axios'
import type { Stock } from 'src/types/stock'
// import { HttpStatusCode } from 'axios';

export class StockService {
  static path = 'stock'

  static async getAll() {
    const res = await api.get(this.path)
    console.log('Call From Services')
    console.log(res.data)
    return res.data
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`)
    return res.data
  }

  static async updateOne(id: number, obj: Partial<Stock>) {
    console.log(obj)
    const res = await api.put(`${this.path}/${id}/`, obj)
    console.log(res.data)
    return res.data
  }
  static async getAllByFilter(search = '', filter = '', branch = '') {
    console.log(branch)
    const res = await api.post(this.path, {
      search,
      filter,
      branch,
    })
    console.log('Call From Services')
    console.log(res.data)
    return res.data
  }
  static async getAllByFilterDialog(search = '', filter = '', branch = '') {
    console.log(branch)
    const res = await api.post(this.path, {
      search,
      filter,
      branch,
    })
    console.log('Call From Services')
    console.log(res.data)
    return res.data
  }
}
