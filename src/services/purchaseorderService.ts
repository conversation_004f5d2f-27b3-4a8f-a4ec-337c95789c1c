import axios from 'axios'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import type { PurchaseOrderItems } from 'src/types/purchaseOrderitems'

const API_URL = 'http://localhost:3000/purchaseorder'
const API_URL2 = 'http://localhost:3000/purchaseorderitem' // เปลี่ยนให้ตรงกับ backend ของคุณ
// เปลี่ยนให้ตรงกับ backend ของคุณ

export const purchaseOrderService = {
  // ดึงข้อมูลรายการ Purchase Orders ทั้งหมด
  async getAll(): Promise<PurchaseOrder[]> {
    const response = await axios.get(API_URL)
    return response.data
  },

  // ดึงข้อมูล Purchase Order ตาม ID
  async getById(id: number): Promise<PurchaseOrder> {
    const response = await axios.get(`${API_URL}/${id}`)
    return response.data
  },

  async getPOItemByPOId(id: number): Promise<PurchaseOrderItems[]> {
    const response = await axios.get(`${API_URL}/${id}/purchaseorderitem`)
    return response.data
  },
  async deletePoItems(selectedIds: number[]) {
    console.log(selectedIds)
    await axios.delete(`${API_URL2}/${selectedIds.join(",")}`, { data: { ids: selectedIds } })
  },

  // สร้าง Purchase Order ใหม่
  async create(order: PurchaseOrder): Promise<PurchaseOrder> {
    const payload = {
      ...order,
      order_date: order.order_date.toISOString(), // 💡 แปลงเป็น string ISO ตอนส่งเท่านั้น
    }
    const response = await axios.post(API_URL, payload)
    return response.data
  },

  // อัปเดต Purchase Order
  async update(id: number, order: Partial<PurchaseOrder>): Promise<PurchaseOrder> {
    const response = await axios.put(`${API_URL}/${id}`, order)
    return response.data
  },

  // ลบ Purchase Order
  async delete(id: number): Promise<void> {
    await axios.delete(`${API_URL}/${id}`)
  },

  async filter(search: string, filter: string, startDate: string, endDate: string): Promise<PurchaseOrder[]> {
    try {
      const response = await axios.post(`${API_URL}/filter`, {
        search,
        filter,
        startDate,
        endDate
      })
      return response.data
    } catch (error) {
      console.error("Error filtering purchase orders", error)
      throw error
    }
  },
}
