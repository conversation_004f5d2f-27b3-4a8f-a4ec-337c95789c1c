import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn } from "typeorm";
import { User } from "./User";

@Entity()
export class LeaveRequest {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => User, user => user.leaveRequests, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @Column({ type: "date" })
  leave_date!: string;

  @Column({ type: "text" })
  leave_type!: string; // "sick" or "personal"

  @Column({ type: "text", nullable: true })
  reason?: string;

  @CreateDateColumn()
  created_at!: Date;
}
