import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { Supplier } from "./Supplier";
import { User } from "./User";
import { PurchaseOrderItem } from "./PurchaseOrderItem";

@Entity()
export class PurchaseOrder {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "text", unique: true })
  code!: string;

  @ManyToOne(() => Supplier, { onDelete: "CASCADE" })
  @JoinColumn()
  supplier!: Supplier;

  @Column({ type: "text" })
  contact!: string;

  @Column({ type: "text" })
  address!: string;

  @Column({ type: "date" })
  date!: Date;

  @ManyToOne(() => User)
  @JoinColumn()
  user!: User;

  @Column({ type: "numeric", default: 0 })
  po_total!: number;

  @Column({ type: "numeric", default: 0 })
  tax!: number;

  @Column({ type: "numeric", default: 0 })
  tax_total!: number;

  @Column({ type: "text" })
  status!: string;

  @Column({ type: "datetime" })
  order_date!: Date;

  @Column({ type: "numeric", default: 0 })
  order_discount!: number;

  @Column({ type: "text", nullable: true })
  note!: string;

  @Column({ type: "numeric", default: 0 })
  order_total!: number;

  @Column({ type: "numeric", default: 0 })
  receive_total!: number;

  @Column({ type: "numeric", default: 0 })
  product_price_tax!: number;

  @Column({ type: "numeric", default: 0 })
  order_discount_tax!: number;

  @Column({ type: "text" })
  receive_status!: string;

  @OneToMany(() => PurchaseOrderItem, (purchaseOrderItem) => purchaseOrderItem.purchase_order)
  purchase_order_items!: PurchaseOrderItem[];


}