import { En<PERSON>ty, PrimaryGeneratedColumn, Column, OneToOne, JoinColumn, ManyToOne } from "typeorm";
import { Product } from "./Product";
import { Branch } from "./Branch";

@Entity()
export class Stock {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  branch!: Branch;

  @Column({ type: "integer" })
  remaining!: number;

  @Column({ type: "text", nullable: true })
  status?: string;
}
