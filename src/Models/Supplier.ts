import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from "typeorm";
import { SupplierType } from "./SupplierType"; 

@Entity()
export class Supplier {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "text" })
  supplier_number: string;

  @Column({ type: "text" })
  name: string;

  @Column({ type: "text", nullable: true }) 
  address?: string;

  @Column({ type: "text", nullable: true }) 
  tel?: string;

  @Column({ type: "text", nullable: true }) 
  tax_number?: string;

  @Column({ type: "text", nullable: true }) 
  contact_name?: string;

  @Column({ type: "text", nullable: true }) 
  fax?: string;

  @Column({ type: "text", nullable: true }) 
  email?: string;

  @Column({ type: "boolean", default: true })
  isactive!: boolean;

  @ManyToOne(() => SupplierType, supplierType => supplierType.id, { onDelete: "CASCADE" })
  type!: SupplierType;

  constructor() {
    this.supplier_number="";
    this.name = ""; 
    this.address = "";
    this.tel = "";
    this.tax_number = "";
    this.contact_name = "";
    this.fax="";
    this.email = "";
  }
}
