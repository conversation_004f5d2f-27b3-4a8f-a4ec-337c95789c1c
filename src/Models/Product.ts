import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from "typeorm";
import { Supplier } from "./Supplier";
import { ProductGroup } from "./ProductGroup";
import { SpecialReportGroup } from "./SpecialReportGroup";

@Entity()
export class Product {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "text", unique: true })
  product_code!: string;

  @Column({ type: "text" })
  product_name!: string;

  @Column({ type: "text", nullable: true })
  generic_name?: string;

  @Column({ type: "numeric" })
  standard_cost!: number;

  @Column({ type: "numeric", nullable: true })
  selling_price!: number;

  @Column({ type: "text", nullable: true })
  storage_location?: string;

  @Column({ type: "integer" })
  stock_min!: number;

  @Column({ type: "integer" })
  stock_max!: number;

  @Column({ type: "text", nullable: true })
  packing_size?: string;

  @Column({ type: "text", nullable: true })
  reg_no?: string;

  @ManyToOne(() => Supplier, supplier => supplier.id ,{ onDelete: "CASCADE" })
  manufacturer?: Supplier;

  @ManyToOne(() => Supplier, supplier => supplier.id ,{ onDelete: "CASCADE" })
  distributor?: Supplier;

  @Column({ type: "text", nullable: true })
  indications?: string;

  @Column({ type: "text", nullable: true })
  warnings?: string;

  @Column({ type: "text", nullable: true })
  purchase_notes?: string;

  @Column({ type: "text", nullable: true })
  cost_notes?: string;

  @Column({ type: "text", nullable: true })
  sales_alert_message?: string;

  @Column({ type: "text", nullable: true })
  generic_group?: string;

  @ManyToOne(() => ProductGroup,productgroup => productgroup.id ,  {onDelete: "CASCADE" })
  product_group?: ProductGroup;

  @Column({ type: "text", nullable: true })
  wholesale_control_group?: string;

  @ManyToOne(() => SpecialReportGroup, specialreportgroup => specialreportgroup.id ,{ onDelete: "CASCADE" })
  special_report_group?: SpecialReportGroup;

  @Column({ type: "text", nullable: true })
  unit?: string;

  @Column({ type: "text", nullable: true })
  barcode?: string;

  @Column({ type: "boolean", default: true })
  isactive!: boolean;

  @Column({ type: "boolean", default: false })
  wholesale!: boolean;

  @Column({ type: "numeric", nullable: true })
  wholesale1?: number;

  @Column({ type: "numeric", nullable: true })
  wholesale2?: number;

  @Column({ type: "numeric", nullable: true })
  wholesale3?: number;

  @Column({ type: "numeric", nullable: true })
  wholesale4?: number;

  @Column({ type: "numeric", nullable: true })
  wholesale5?: number;

  @Column({ type: "boolean", default: false })
  retail!: boolean;

  @Column({ type: "numeric", nullable: true })
  retail1?: number;

  @Column({ type: "numeric", nullable: true })
  retail2?: number;

  @Column({ type: "numeric", nullable: true })
  retail3?: number;

  @Column({ type: "numeric", nullable: true })
  retail4?: number;

  @Column({ type: "numeric", nullable: true })
  retail5?: number;

  // @Column({ type: "integer" })
  // remaining!: number;

  // @Column({ type: "text", nullable: true })
  // status?: string;
  
}
